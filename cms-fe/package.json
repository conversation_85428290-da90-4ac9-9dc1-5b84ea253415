{"name": "creasoft-cms-frontend", "version": "1.0.0", "description": "Next.js frontend for CreaSoft CMS Panel", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@headlessui/react": "^1.7.17", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.8.4", "@tiptap/extension-color": "^3.0.9", "@tiptap/extension-font-family": "^3.0.9", "@tiptap/extension-font-size": "^3.0.0-next.3", "@tiptap/extension-horizontal-rule": "^3.0.9", "@tiptap/extension-image": "^2.1.13", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-placeholder": "^2.1.13", "@tiptap/extension-text-align": "^3.0.9", "@tiptap/extension-text-style": "^3.0.9", "@tiptap/extension-underline": "^3.0.9", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "js-cookie": "^3.0.5", "lucide-react": "^0.294.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/node": "^20.10.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.6", "typescript": "^5.3.2"}}