'use client';

import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Loader2, User, Mail, Lock, Shield } from 'lucide-react';
import { useCreateUser } from '@/hooks/use-users';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const userSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  role: z.enum(['admin', 'editor', 'viewer']).default('editor'),
});

type UserForm = z.infer<typeof userSchema>;

export default function NewUserPage() {
  const router = useRouter();
  const createUser = useCreateUser();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<UserForm>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      role: 'editor',
    },
  });

  const onSubmit = async (data: UserForm) => {
    try {
      await createUser.mutateAsync(data);
      router.push('/users');
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const selectedRole = watch('role');

  const getRoleInfo = (role: string) => {
    switch (role) {
      case 'admin':
        return {
          color: 'destructive',
          description: 'Full access to all features and settings',
          icon: Shield,
        };
      case 'editor':
        return {
          color: 'warning',
          description: 'Can create and manage content',
          icon: User,
        };
      case 'viewer':
        return {
          color: 'secondary',
          description: 'Read-only access to content',
          icon: User,
        };
      default:
        return {
          color: 'secondary',
          description: '',
          icon: User,
        };
    }
  };

  const roleInfo = getRoleInfo(selectedRole);

  return (
    <DashboardLayout>
      <div className="p-6 max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
            className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Add New User</h1>
            <p className="text-[#CBD5E1]">Create a new user account for your team</p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card className="card-dark">
            <CardHeader className="border-b border-[#334155]">
              <CardTitle className="text-white flex items-center">
                <User className="h-5 w-5 mr-2 text-[#3B82F6]" />
                Basic Information
              </CardTitle>
              <CardDescription className="text-[#94A3B8]">
                Personal details for the new user
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-white mb-2">
                    First Name *
                  </label>
                  <Input
                    id="firstName"
                    placeholder="John"
                    {...register('firstName')}
                    className={errors.firstName ? 'border-red-500 focus:border-red-500' : ''}
                  />
                  {errors.firstName && (
                    <p className="mt-2 text-sm text-red-400">{errors.firstName.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-white mb-2">
                    Last Name *
                  </label>
                  <Input
                    id="lastName"
                    placeholder="Doe"
                    {...register('lastName')}
                    className={errors.lastName ? 'border-red-500 focus:border-red-500' : ''}
                  />
                  {errors.lastName && (
                    <p className="mt-2 text-sm text-red-400">{errors.lastName.message}</p>
                  )}
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-white mb-2">
                  Email Address *
                </label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  className={errors.email ? 'border-red-500 focus:border-red-500' : ''}
                />
                {errors.email && (
                  <p className="mt-2 text-sm text-red-400">{errors.email.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-white mb-2">
                  Password *
                </label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter a secure password"
                  {...register('password')}
                  className={errors.password ? 'border-red-500 focus:border-red-500' : ''}
                />
                {errors.password && (
                  <p className="mt-2 text-sm text-red-400">{errors.password.message}</p>
                )}
                <p className="mt-1 text-xs text-[#94A3B8]">
                  Password must be at least 6 characters long
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Role Selection */}
          <Card className="card-dark">
            <CardHeader className="border-b border-[#334155]">
              <CardTitle className="text-white flex items-center">
                <Shield className="h-5 w-5 mr-2 text-[#3B82F6]" />
                Role & Permissions
              </CardTitle>
              <CardDescription className="text-[#94A3B8]">
                Choose the user's role and access level
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-3">
                    User Role *
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {['admin', 'editor', 'viewer'].map((role) => {
                      const info = getRoleInfo(role);
                      const Icon = info.icon;
                      return (
                        <div
                          key={role}
                          className={`p-4 border rounded-lg cursor-pointer transition-all ${
                            selectedRole === role
                              ? 'border-[#3B82F6] bg-[#3B82F6]/10'
                              : 'border-[#475569] hover:border-[#64748B]'
                          }`}
                          onClick={() => setValue('role', role as any)}
                        >
                          <div className="flex items-center space-x-3 mb-2">
                            <Icon className="h-5 w-5 text-[#3B82F6]" />
                            <span className="font-medium text-white capitalize">{role}</span>
                            <Badge variant={info.color as any} className="ml-auto">
                              {role}
                            </Badge>
                          </div>
                          <p className="text-sm text-[#94A3B8]">{info.description}</p>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              className="border-[#475569] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createUser.isPending}
              className="btn-primary"
            >
              {createUser.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating User...
                </>
              ) : (
                'Create User'
              )}
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
