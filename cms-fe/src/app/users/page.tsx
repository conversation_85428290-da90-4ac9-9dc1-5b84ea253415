'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { 
  Users as UsersIcon, 
  Plus, 
  Search, 
  Filter,
  Edit, 
  Trash2, 
  UserCheck,
  UserX,
  Mail,
  Shield,
  Crown,
  User,
  MoreHorizontal
} from 'lucide-react';
import { useUsers, useDeleteUser, useToggleUserStatus, useResetUserPassword } from '@/hooks/use-users';
import { useAuth } from '@/hooks/use-auth';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate, formatRelativeTime } from '@/lib/utils';

function UserCard({ user: userItem, currentUser }: { user: any; currentUser: any }) {
  const router = useRouter();
  const deleteUser = useDeleteUser();
  const toggleUserStatus = useToggleUserStatus();
  const resetUserPassword = useResetUserPassword();

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return Crown;
      case 'editor':
        return Shield;
      case 'author':
        return Edit;
      default:
        return User;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'destructive';
      case 'editor':
        return 'warning';
      case 'author':
        return 'success';
      default:
        return 'secondary';
    }
  };

  const handleDelete = async () => {
    if (userItem._id === currentUser?.id) {
      alert('You cannot delete your own account');
      return;
    }
    
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      deleteUser.mutate(userItem._id);
    }
  };

  const handleToggleStatus = async () => {
    if (userItem._id === currentUser?.id) {
      alert('You cannot change your own status');
      return;
    }
    
    const action = userItem.isActive ? 'deactivate' : 'activate';
    if (window.confirm(`Are you sure you want to ${action} this user?`)) {
      toggleUserStatus.mutate(userItem._id);
    }
  };

  const handleResetPassword = async () => {
    if (window.confirm('Are you sure you want to send a password reset email to this user?')) {
      resetUserPassword.mutate(userItem._id);
    }
  };

  const RoleIcon = getRoleIcon(userItem.role);

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
              <User className="h-6 w-6 text-gray-600" />
            </div>
            <div>
              <CardTitle className="text-lg">
                {userItem.firstName} {userItem.lastName}
              </CardTitle>
              <CardDescription className="flex items-center space-x-2">
                <span>{userItem.email}</span>
                <Badge variant={userItem.isActive ? 'success' : 'secondary'}>
                  {userItem.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Link href={`/users/${userItem._id}/edit`}>
              <Button variant="ghost" size="icon">
                <Edit className="h-4 w-4" />
              </Button>
            </Link>
            {userItem._id !== currentUser?.id && (
              <Button
                variant="ghost"
                size="icon"
                onClick={handleDelete}
                disabled={deleteUser.isPending}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Role:</span>
            <Badge variant={getRoleColor(userItem.role)} className="flex items-center space-x-1">
              <RoleIcon className="h-3 w-3" />
              <span className="capitalize">{userItem.role}</span>
            </Badge>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Joined:</span>
            <span>{formatDate(userItem.createdAt)}</span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Last Login:</span>
            <span>
              {userItem.lastLoginAt 
                ? formatRelativeTime(userItem.lastLoginAt)
                : 'Never'
              }
            </span>
          </div>

          <div className="pt-2 border-t flex items-center space-x-2">
            {userItem._id !== currentUser?.id && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleToggleStatus}
                  disabled={toggleUserStatus.isPending}
                >
                  {userItem.isActive ? (
                    <>
                      <UserX className="h-4 w-4 mr-1" />
                      Deactivate
                    </>
                  ) : (
                    <>
                      <UserCheck className="h-4 w-4 mr-1" />
                      Activate
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleResetPassword}
                  disabled={resetUserPassword.isPending}
                >
                  <Mail className="h-4 w-4 mr-1" />
                  Reset Password
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function UserCardSkeleton() {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex justify-between">
            <Skeleton className="h-4 w-12" />
            <Skeleton className="h-5 w-16" />
          </div>
          <div className="flex justify-between">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex justify-between">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function UsersPage() {
  const { user: currentUser } = useAuth();
  const [search, setSearch] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');

  const { data: users, isLoading } = useUsers({ 
    search, 
    role: roleFilter || undefined,
    status: statusFilter || undefined
  });

  const filteredUsers = users?.data || [];

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Users</h1>
            <p className="text-gray-600">
              Manage team members, roles, and permissions
            </p>
          </div>
          {currentUser?.role === 'admin' && (
            <Link href="/users/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </Link>
          )}
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search users..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All roles</option>
            <option value="admin">Admin</option>
            <option value="editor">Editor</option>
            <option value="author">Author</option>
          </select>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>

        {/* Stats */}
        {users && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">{users.total}</div>
                <p className="text-xs text-muted-foreground">Total users</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">
                  {users.data?.filter((u: any) => u.isActive).length || 0}
                </div>
                <p className="text-xs text-muted-foreground">Active</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">
                  {users.data?.filter((u: any) => u.role === 'admin').length || 0}
                </div>
                <p className="text-xs text-muted-foreground">Admins</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">
                  {users.data?.filter((u: any) => u.role === 'editor').length || 0}
                </div>
                <p className="text-xs text-muted-foreground">Editors</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Users Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <UserCardSkeleton key={i} />
            ))}
          </div>
        ) : filteredUsers.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredUsers.map((user: any) => (
              <UserCard key={user._id} user={user} currentUser={currentUser} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <UsersIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
            <p className="text-gray-600 mb-4">
              {search || roleFilter || statusFilter 
                ? 'No users match your current filters.' 
                : 'Get started by adding your first team member.'}
            </p>
            {currentUser?.role === 'admin' && (
              <Link href="/users/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add User
                </Button>
              </Link>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
