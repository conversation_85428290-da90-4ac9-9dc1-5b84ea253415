'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { 
  Globe, 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Key,
  ExternalLink,
  Copy
} from 'lucide-react';
import { useWebsites, useDeleteWebsite, useRegenerateApiKey } from '@/hooks/use-websites';
import { useAuth } from '@/hooks/use-auth';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { cn, formatDate } from '@/lib/utils';
import toast from 'react-hot-toast';

function WebsiteCard({ website }: { website: any }) {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const deleteWebsite = useDeleteWebsite();
  const regenerateApiKey = useRegenerateApiKey();

  const handleCopyApiKey = async () => {
    try {
      await navigator.clipboard.writeText(website.apiKey);
      toast.success('API key copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy API key');
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this website? This action cannot be undone.')) {
      deleteWebsite.mutate(website._id);
    }
  };

  const handleRegenerateApiKey = async () => {
    if (window.confirm('Are you sure you want to regenerate the API key? The old key will stop working immediately.')) {
      regenerateApiKey.mutate(website._id);
    }
  };

  return (
    <Card className="card-dark group hover:scale-[1.02] transition-all duration-300">
      <CardHeader className="border-b border-[#334155]">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-[#3B82F6] to-[#1E40AF] flex items-center justify-center">
              <Globe className="h-6 w-6 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg text-white group-hover:text-[#60A5FA] transition-colors">{website.name}</CardTitle>
              <CardDescription className="flex items-center space-x-2 text-[#94A3B8]">
                <span>{website.domain}</span>
                <Badge variant={website.isActive ? 'success' : 'secondary'}>
                  {website.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push(`/websites/${website._id}`)}
              className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
            {(currentUser?.role === 'admin' || website.ownerId === currentUser?._id) && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.push(`/websites/${website._id}/edit`)}
                className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
              >
                <Edit className="h-4 w-4" />
              </Button>
            )}
            {currentUser?.role === 'admin' && (
              <Button
                variant="ghost"
                size="icon"
                onClick={handleDelete}
                disabled={deleteWebsite.isPending}
                className="text-[#CBD5E1] hover:text-[#EF4444] hover:bg-[#334155]"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-4">
          {website.description && (
            <p className="text-sm text-[#94A3B8]">{website.description}</p>
          )}

          <div className="flex items-center justify-between text-sm">
            <span className="text-[#94A3B8]">Blogs:</span>
            <span className="font-medium text-white bg-[#334155] px-2 py-1 rounded-lg">{website.blogCount || 0}</span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-[#94A3B8]">Theme:</span>
            <Badge variant="outline" className="capitalize border-[#334155] text-[#CBD5E1]">
              {website.theme}
            </Badge>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-[#94A3B8]">Created:</span>
            <span className="text-[#CBD5E1]">{formatDate(website.createdAt)}</span>
          </div>

          <div className="pt-4 border-t border-[#334155]">
            <div className="flex items-center justify-between">
              <span className="text-sm text-[#94A3B8]">API Key:</span>
              <div className="flex items-center space-x-2">
                <code className="text-xs bg-[#334155] text-[#CBD5E1] px-3 py-1 rounded-lg font-mono">
                  {website.apiKey.substring(0, 20)}...
                </code>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
                  onClick={handleCopyApiKey}
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
                  onClick={handleRegenerateApiKey}
                  disabled={regenerateApiKey.isPending}
                >
                  <Key className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function WebsiteCardSkeleton() {
  return (
    <Card className="card-dark animate-pulse">
      <CardHeader className="border-b border-[#334155]">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Skeleton className="h-12 w-12 rounded-xl bg-[#334155]" />
            <div className="space-y-2">
              <Skeleton className="h-5 w-32 bg-[#334155]" />
              <Skeleton className="h-4 w-24 bg-[#334155]" />
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <Skeleton className="h-8 w-8 bg-[#334155]" />
            <Skeleton className="h-8 w-8 bg-[#334155]" />
            <Skeleton className="h-8 w-8 bg-[#334155]" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-4">
          <Skeleton className="h-4 w-full bg-[#334155]" />
          <Skeleton className="h-4 w-3/4 bg-[#334155]" />
          <Skeleton className="h-4 w-1/2 bg-[#334155]" />
        </div>
      </CardContent>
    </Card>
  );
}

export default function WebsitesPage() {
  const [search, setSearch] = useState('');
  const { user: currentUser } = useAuth();
  const { data: websites, isLoading } = useWebsites({ search });

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 flex items-center">
              <Globe className="h-8 w-8 mr-3 text-[#3B82F6]" />
              <span className="bg-gradient-to-r from-[#3B82F6] to-[#60A5FA] bg-clip-text text-transparent">
                Websites
              </span>
            </h1>
            <p className="text-[#CBD5E1] text-lg">
              Manage your websites and their configurations
            </p>
          </div>
          {currentUser?.role === 'admin' && (
            <Link href="/websites/new">
              <Button className="btn-primary">
                <Plus className="h-4 w-4 mr-2" />
                Create Website
              </Button>
            </Link>
          )}
        </div>

        {/* Search and Filters */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#94A3B8]" />
            <Input
              placeholder="Search websites..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 input-dark"
            />
          </div>
        </div>

        {/* Websites Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <WebsiteCardSkeleton key={i} />
            ))}
          </div>
        ) : websites?.data?.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {websites.data.map((website: any) => (
              <WebsiteCard key={website._id} website={website} />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-gradient-to-br from-[#3B82F6] to-[#1E40AF] rounded-full flex items-center justify-center mx-auto mb-6">
              <Globe className="h-10 w-10 text-white" />
            </div>
            <h3 className="text-xl font-medium text-white mb-2">No websites found</h3>
            <p className="text-[#94A3B8] mb-6 max-w-md mx-auto">
              {search ? 'No websites match your search criteria.' : 'Get started by creating your first website to begin managing your content.'}
            </p>
            <Link href="/websites/new">
              <Button className="btn-primary">
                <Plus className="h-4 w-4 mr-2" />
                Create Website
              </Button>
            </Link>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
