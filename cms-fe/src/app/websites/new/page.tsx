'use client';

import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { useCreateWebsite } from '@/hooks/use-websites';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const websiteSchema = z.object({
  name: z.string().min(2, 'Website name must be at least 2 characters'),
  domain: z.string().min(3, 'Domain must be at least 3 characters'),
  description: z.string().optional(),
  theme: z.enum(['minimal', 'magazine', 'corporate']).default('minimal'),
  seoSettings: z.object({
    defaultTitle: z.string().optional(),
    defaultDescription: z.string().optional(),
    favicon: z.string().url().optional().or(z.literal('')),
    ogImage: z.string().url().optional().or(z.literal('')),
    googleAnalyticsId: z.string().optional(),
    googleSearchConsoleId: z.string().optional(),
  }).optional(),
});

type WebsiteForm = z.infer<typeof websiteSchema>;

export default function NewWebsitePage() {
  const router = useRouter();
  const createWebsite = useCreateWebsite();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<WebsiteForm>({
    resolver: zodResolver(websiteSchema),
    defaultValues: {
      theme: 'minimal',
      seoSettings: {},
    },
  });

  const onSubmit = async (data: WebsiteForm) => {
    try {
      await createWebsite.mutateAsync(data);
      router.push('/websites');
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center space-x-4 mb-8">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
              className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-white">Create Website</h1>
              <p className="text-[#CBD5E1]">
                Add a new website to manage blogs and content
              </p>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <Card className="card-dark">
              <CardHeader className="border-b border-[#334155]">
                <CardTitle className="text-white">Basic Information</CardTitle>
                <CardDescription className="text-[#94A3B8]">
                  Essential details about your website
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 p-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-[#F8FAFC] mb-2">
                    Website Name *
                  </label>
                  <Input
                    id="name"
                    placeholder="My Awesome Blog"
                    {...register('name')}
                    className={`input-dark ${errors.name ? 'border-[#EF4444] focus:border-[#EF4444]' : ''}`}
                  />
                  {errors.name && (
                    <p className="mt-2 text-sm text-[#EF4444] flex items-center">
                      <span className="w-1 h-1 bg-[#EF4444] rounded-full mr-2"></span>
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div>
                  <label htmlFor="domain" className="block text-sm font-medium text-[#F8FAFC] mb-2">
                    Domain *
                  </label>
                  <Input
                    id="domain"
                    placeholder="myawesomeblog.com"
                    {...register('domain')}
                    className={`input-dark ${errors.domain ? 'border-[#EF4444] focus:border-[#EF4444]' : ''}`}
                  />
                  {errors.domain && (
                    <p className="mt-2 text-sm text-[#EF4444] flex items-center">
                      <span className="w-1 h-1 bg-[#EF4444] rounded-full mr-2"></span>
                      {errors.domain.message}
                    </p>
                  )}
                  <p className="mt-1 text-xs text-[#94A3B8]">
                    Enter your website's domain name (without http://)
                  </p>
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-[#F8FAFC] mb-2">
                    Description
                  </label>
                  <Input
                    id="description"
                    placeholder="A brief description of your website"
                    {...register('description')}
                    className="input-dark"
                  />
                </div>

                <div>
                  <label htmlFor="theme" className="block text-sm font-medium text-[#F8FAFC] mb-2">
                    Theme
                  </label>
                  <select
                    id="theme"
                    {...register('theme')}
                    className="w-full px-4 py-3 border border-[#334155] bg-[#1A1A2E] text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-[#3B82F6] transition-all"
                  >
                    <option value="minimal">Minimal</option>
                    <option value="magazine">Magazine</option>
                    <option value="corporate">Corporate</option>
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* SEO Settings */}
            <Card className="card-dark">
              <CardHeader className="border-b border-[#334155]">
                <CardTitle className="text-white">SEO Settings</CardTitle>
                <CardDescription className="text-[#94A3B8]">
                  Configure default SEO settings for your website
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 p-6">
                <div>
                  <label htmlFor="defaultTitle" className="block text-sm font-medium text-[#F8FAFC] mb-2">
                    Default Title
                  </label>
                  <Input
                    id="defaultTitle"
                    placeholder="My Awesome Blog - Latest News and Updates"
                    {...register('seoSettings.defaultTitle')}
                    className="input-dark"
                  />
                </div>

                <div>
                  <label htmlFor="defaultDescription" className="block text-sm font-medium text-[#F8FAFC] mb-2">
                    Default Description
                  </label>
                  <Input
                    id="defaultDescription"
                    placeholder="Stay updated with the latest news, tips, and insights from our blog"
                    {...register('seoSettings.defaultDescription')}
                    className="input-dark"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="favicon" className="block text-sm font-medium text-[#F8FAFC] mb-2">
                      Favicon URL
                    </label>
                    <Input
                      id="favicon"
                      placeholder="https://example.com/favicon.ico"
                      {...register('seoSettings.favicon')}
                      className="input-dark"
                    />
                  </div>

                  <div>
                    <label htmlFor="ogImage" className="block text-sm font-medium text-[#F8FAFC] mb-2">
                      Default OG Image
                    </label>
                    <Input
                      id="ogImage"
                      placeholder="https://example.com/og-image.jpg"
                      {...register('seoSettings.ogImage')}
                      className="input-dark"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="googleAnalyticsId" className="block text-sm font-medium text-[#F8FAFC] mb-2">
                      Google Analytics ID
                    </label>
                    <Input
                      id="googleAnalyticsId"
                      placeholder="GA-XXXXXXXXX-X"
                      {...register('seoSettings.googleAnalyticsId')}
                      className="input-dark"
                    />
                  </div>

                  <div>
                    <label htmlFor="googleSearchConsoleId" className="block text-sm font-medium text-[#F8FAFC] mb-2">
                      Search Console ID
                    </label>
                    <Input
                      id="googleSearchConsoleId"
                      placeholder="google-site-verification=..."
                      {...register('seoSettings.googleSearchConsoleId')}
                      className="input-dark"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                className="border-[#334155] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createWebsite.isPending}
                className="btn-primary"
              >
                {createWebsite.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create Website'
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </DashboardLayout>
  );
}
