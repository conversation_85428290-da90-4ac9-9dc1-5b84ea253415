'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Key, 
  Copy, 
  ExternalLink,
  Globe,
  FileText,
  Eye,
  Share2
} from 'lucide-react';
import { useWebsite, useDeleteWebsite, useRegenerateApiKey } from '@/hooks/use-websites';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { IntegrationDocs } from '@/components/website/integration-docs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatDate } from '@/lib/utils';
import toast from 'react-hot-toast';

interface WebsiteDetailPageProps {
  params: {
    id: string;
  };
}

export default function WebsiteDetailPage({ params }: WebsiteDetailPageProps) {
  const router = useRouter();
  const { data: website, isLoading } = useWebsite(params.id);
  const deleteWebsite = useDeleteWebsite();
  const regenerateApiKey = useRegenerateApiKey();
  const [showFullApiKey, setShowFullApiKey] = useState(false);

  const handleCopyApiKey = async () => {
    try {
      await navigator.clipboard.writeText(website.apiKey);
      toast.success('API key copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy API key');
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this website? This action cannot be undone.')) {
      try {
        await deleteWebsite.mutateAsync(params.id);
        router.push('/websites');
      } catch (error) {
        // Error is handled by the mutation
      }
    }
  };

  const handleRegenerateApiKey = async () => {
    if (window.confirm('Are you sure you want to regenerate the API key? The old key will stop working immediately.')) {
      regenerateApiKey.mutate(params.id);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center space-x-4 mb-8">
              <Skeleton className="h-10 w-10" />
              <div className="space-y-2">
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <Skeleton className="h-6 w-32" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardContent>
                </Card>
              </div>
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <Skeleton className="h-6 w-24" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!website) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Website not found</h3>
            <p className="text-gray-600 mb-4">
              The website you're looking for doesn't exist or you don't have access to it.
            </p>
            <Link href="/websites">
              <Button>Back to Websites</Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
                className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div className="flex items-center space-x-3">
                <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-[#3B82F6] to-[#1E40AF] flex items-center justify-center">
                  <Globe className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-white">{website.name}</h1>
                  <div className="flex items-center space-x-2">
                    <span className="text-[#CBD5E1]">{website.domain}</span>
                    <Badge variant={website.isActive ? 'success' : 'secondary'}>
                      {website.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Link href={`/websites/${params.id}/edit`}>
                <Button variant="outline" className="border-[#334155] text-[#CBD5E1] hover:bg-[#334155] hover:text-white">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              </Link>
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={deleteWebsite.isPending}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>

          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-[#1A1A2E] border border-[#334155]">
              <TabsTrigger value="overview" className="text-[#CBD5E1] data-[state=active]:bg-[#3B82F6] data-[state=active]:text-white">Overview</TabsTrigger>
              <TabsTrigger value="integration" className="text-[#CBD5E1] data-[state=active]:bg-[#3B82F6] data-[state=active]:text-white">Integration</TabsTrigger>
              <TabsTrigger value="settings" className="text-[#CBD5E1] data-[state=active]:bg-[#3B82F6] data-[state=active]:text-white">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Content */}
                <div className="lg:col-span-2 space-y-6">
              {/* Website Information */}
              <Card className="card-dark">
                <CardHeader className="border-b border-[#334155]">
                  <CardTitle className="text-white">Website Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 p-6">
                  {website.description && (
                    <div>
                      <label className="text-sm font-medium text-[#94A3B8]">Description</label>
                      <p className="text-white mt-1">{website.description}</p>
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-[#94A3B8]">Theme</label>
                      <p className="text-white capitalize mt-1">{website.theme}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-[#94A3B8]">Blog Count</label>
                      <p className="text-white mt-1">{website.blogCount || 0}</p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-[#94A3B8]">Created</label>
                    <p className="text-white mt-1">{formatDate(website.createdAt)}</p>
                  </div>
                </CardContent>
              </Card>


            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* API Key */}
              <Card className="card-dark">
                <CardHeader className="border-b border-[#334155]">
                  <CardTitle className="text-white">API Key</CardTitle>
                  <CardDescription className="text-[#94A3B8]">
                    Use this key to access your website's content via API
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 p-6">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <label className="text-sm font-medium text-[#94A3B8]">API Key</label>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowFullApiKey(!showFullApiKey)}
                        className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
                      >
                        {showFullApiKey ? 'Hide' : 'Show'}
                      </Button>
                    </div>
                    <div className="flex items-center space-x-2">
                      <code className="flex-1 text-xs bg-[#1A1A2E] text-[#CBD5E1] px-3 py-2 rounded-lg font-mono border border-[#334155]">
                        {showFullApiKey ? website.apiKey : `${website.apiKey.substring(0, 20)}...`}
                      </code>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleCopyApiKey}
                        className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    className="w-full border-[#334155] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
                    onClick={handleRegenerateApiKey}
                    disabled={regenerateApiKey.isPending}
                  >
                    <Key className="h-4 w-4 mr-2" />
                    Regenerate Key
                  </Button>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="card-dark">
                <CardHeader className="border-b border-[#334155]">
                  <CardTitle className="text-white">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 p-6">
                  <Link href={`/blogs?websiteId=${params.id}`}>
                    <Button variant="outline" className="w-full justify-start border-[#334155] text-[#CBD5E1] hover:bg-[#334155] hover:text-white">
                      <FileText className="h-4 w-4 mr-2" />
                      View Blogs
                    </Button>
                  </Link>
                  <Link href={`/blogs/new?websiteId=${params.id}`}>
                    <Button variant="outline" className="w-full justify-start border-[#334155] text-[#CBD5E1] hover:bg-[#334155] hover:text-white">
                      <FileText className="h-4 w-4 mr-2" />
                      Create Blog
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    className="w-full justify-start border-[#334155] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
                    onClick={() => window.open(`https://${website.domain}`, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Visit Website
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="integration" className="space-y-6">
          <IntegrationDocs website={website} />
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* SEO Settings */}
            <Card>
              <CardHeader>
                <CardTitle>SEO Settings</CardTitle>
                <CardDescription>
                  Search engine optimization settings for your website
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {website.seoSettings?.defaultTitle ? (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Default Title</label>
                    <p className="text-gray-900">{website.seoSettings.defaultTitle}</p>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No SEO title configured</p>
                )}

                {website.seoSettings?.defaultDescription ? (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Default Description</label>
                    <p className="text-gray-900">{website.seoSettings.defaultDescription}</p>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No SEO description configured</p>
                )}

                {website.seoSettings?.googleAnalyticsId && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Google Analytics ID</label>
                    <p className="text-gray-900 font-mono text-sm">{website.seoSettings.googleAnalyticsId}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Advanced Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Advanced Settings</CardTitle>
                <CardDescription>
                  Additional configuration options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Theme</label>
                  <p className="text-gray-900 capitalize">{website.theme}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">Status</label>
                  <div className="flex items-center space-x-2">
                    <Badge variant={website.isActive ? 'success' : 'secondary'}>
                      {website.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">Created</label>
                  <p className="text-gray-900">{formatDate(website.createdAt)}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">Last Updated</label>
                  <p className="text-gray-900">{formatDate(website.updatedAt)}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
        </div>
      </div>
    </DashboardLayout>
  );
}
