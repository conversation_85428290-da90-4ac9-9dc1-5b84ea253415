'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/hooks/use-auth';
import { useDashboardStats, useRecentBlogs } from '@/hooks/use-dashboard';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Loader2,
  Globe,
  FileText,
  Image,
  Users,
  TrendingUp,
  Eye,
  Share2,
  Plus,
  ArrowRight,
  BarChart3,
  Calendar,
  Clock,
  Target
} from 'lucide-react';
import { formatDate, formatRelativeTime, formatFileSize } from '@/lib/utils';

function StatsCard({ title, value, icon: Icon, description, trend }: {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  trend?: { value: number; isPositive: boolean };
}) {
  return (
    <Card className="card-dark group hover:scale-105 transition-all duration-300">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-[#F8FAFC]">{title}</CardTitle>
        <div className="p-2 rounded-lg bg-gradient-to-br from-[#3B82F6] to-[#1E40AF]">
          <Icon className="h-4 w-4 text-white" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-white mb-2">{value}</div>
        <div className="flex items-center space-x-2">
          <p className="text-xs text-[#94A3B8]">{description}</p>
          {trend && (
            <div className={`flex items-center text-xs px-2 py-1 rounded-full ${
              trend.isPositive
                ? 'text-[#10B981] bg-[#10B981] bg-opacity-10'
                : 'text-[#EF4444] bg-[#EF4444] bg-opacity-10'
            }`}>
              <TrendingUp className="h-3 w-3 mr-1" />
              {trend.value}%
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function StatsSkeleton() {
  return (
    <Card className="card-dark animate-pulse">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-20 bg-[#334155]" />
        <Skeleton className="h-4 w-4 bg-[#334155] rounded" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-8 w-16 mb-2 bg-[#334155]" />
        <Skeleton className="h-3 w-24 bg-[#334155]" />
      </CardContent>
    </Card>
  );
}

export default function DashboardPage() {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const { data: stats, isLoading: statsLoading } = useDashboardStats();
  const { data: recentBlogs, isLoading: blogsLoading } = useRecentBlogs();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#0F0F23]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#3B82F6]" />
          <p className="text-[#94A3B8]">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header Section */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-[#3B82F6] to-[#1E40AF] rounded-2xl opacity-10"></div>
          <div className="relative p-6 rounded-2xl border border-[#334155]">
            <h1 className="text-3xl font-bold text-white mb-2 flex items-center">
              <span className="bg-gradient-to-r from-[#3B82F6] to-[#60A5FA] bg-clip-text text-transparent">
                Dashboard
              </span>
            </h1>
            <p className="text-[#CBD5E1] text-lg">
              Welcome back, <span className="text-[#60A5FA] font-medium">{user.firstName}</span>!
              Here's what's happening with your content.
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statsLoading ? (
            <>
              <StatsSkeleton />
              <StatsSkeleton />
              <StatsSkeleton />
              <StatsSkeleton />
            </>
          ) : (
            <>
              <StatsCard
                title="Websites"
                value={stats?.websites?.total || 0}
                icon={Globe}
                description="Active websites"
              />
              <StatsCard
                title="Published Blogs"
                value={stats?.blogs?.published || 0}
                icon={FileText}
                description="Live blog posts"
              />
              <StatsCard
                title="Media Files"
                value={stats?.media?.total || 0}
                icon={Image}
                description={`${stats?.media?.totalSizeMB || 0} MB used`}
              />
              <StatsCard
                title="Total Views"
                value={stats?.blogs?.totalViews || 0}
                icon={Eye}
                description="Blog post views"
              />
            </>
          )}
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 gap-6 mb-8">
          {/* Recent Blogs */}
          <div>
            <Card className="card-dark">
              <CardHeader className="flex flex-row items-center justify-between border-b border-[#334155]">
                <div>
                  <CardTitle className="text-white flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-[#3B82F6]" />
                    Recent Blogs
                  </CardTitle>
                  <CardDescription className="text-[#94A3B8]">Your latest blog posts</CardDescription>
                </div>
                <Link href="/blogs">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-[#334155] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
                  >
                    View All
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardHeader>
              <CardContent className="p-6">
                {blogsLoading ? (
                  <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="flex items-center space-x-4 p-3 rounded-lg bg-[#1E293B] bg-opacity-50">
                        <Skeleton className="h-12 w-12 rounded bg-[#334155]" />
                        <div className="space-y-2 flex-1">
                          <Skeleton className="h-4 w-3/4 bg-[#334155]" />
                          <Skeleton className="h-3 w-1/2 bg-[#334155]" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : recentBlogs?.data?.length > 0 ? (
                  <div className="space-y-3">
                    {recentBlogs.data.slice(0, 5).map((blog: any) => (
                      <div key={blog._id} className="flex items-center space-x-4 p-3 rounded-lg bg-[#1E293B] bg-opacity-50 hover:bg-opacity-70 transition-all duration-200 group">
                        <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-[#3B82F6] to-[#1E40AF] flex items-center justify-center">
                          <FileText className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-white truncate group-hover:text-[#60A5FA] transition-colors">
                            {blog.title}
                          </p>
                          <div className="flex items-center space-x-2 text-xs text-[#94A3B8]">
                            <Badge
                              variant={blog.status === 'published' ? 'default' : 'secondary'}
                              className={blog.status === 'published'
                                ? 'bg-[#10B981] text-white'
                                : 'bg-[#334155] text-[#CBD5E1]'
                              }
                            >
                              {blog.status}
                            </Badge>
                            <span>•</span>
                            <span>{formatRelativeTime(blog.createdAt)}</span>
                            <span>•</span>
                            <span className="flex items-center">
                              <Eye className="h-3 w-3 mr-1" />
                              {blog.viewCount}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-[#3B82F6] to-[#1E40AF] rounded-full flex items-center justify-center mx-auto mb-4">
                      <FileText className="h-8 w-8 text-white" />
                    </div>
                    <p className="text-sm text-[#94A3B8] mb-4">No blogs yet</p>
                    <Link href="/blogs/new">
                      <Button size="sm" className="btn-primary">
                        <Plus className="h-4 w-4 mr-2" />
                        Create your first blog
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            <Card className="card-dark">
              <CardHeader className="border-b border-[#334155]">
                <CardTitle className="text-white flex items-center">
                  <Share2 className="h-5 w-5 mr-2 text-[#3B82F6]" />
                  Quick Actions
                </CardTitle>
                <CardDescription className="text-[#94A3B8]">Common tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3 p-6">
                <Link href="/websites/new">
                  <Button className="w-full justify-start btn-primary group">
                    <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform" />
                    Create Website
                  </Button>
                </Link>
                <Link href="/blogs/new">
                  <Button
                    variant="outline"
                    className="w-full justify-start border-[#334155] text-[#CBD5E1] hover:bg-[#334155] hover:text-white group"
                  >
                    <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform" />
                    Write Blog
                  </Button>
                </Link>
                <Link href="/media">
                  <Button
                    variant="outline"
                    className="w-full justify-start border-[#334155] text-[#CBD5E1] hover:bg-[#334155] hover:text-white group"
                  >
                    <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform" />
                    Upload Media
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Analytics Overview */}
            <Card className="card-dark">
              <CardHeader className="border-b border-[#334155]">
                <CardTitle className="text-white flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-[#3B82F6]" />
                  Analytics Overview
                </CardTitle>
                <CardDescription className="text-[#94A3B8]">Performance insights</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  {/* Performance Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-[#0F172A] rounded-lg p-4 border border-[#334155]">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-[#94A3B8]">Avg. Views/Blog</span>
                        <Eye className="h-4 w-4 text-[#3B82F6]" />
                      </div>
                      <div className="text-xl font-bold text-white">
                        {stats ? Math.round((stats.blogs?.totalViews || 0) / Math.max(stats.blogs?.total || 1, 1)) : 0}
                      </div>
                      <div className="text-xs text-[#10B981]">+12% this month</div>
                    </div>

                    <div className="bg-[#0F172A] rounded-lg p-4 border border-[#334155]">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-[#94A3B8]">Engagement Rate</span>
                        <Target className="h-4 w-4 text-[#10B981]" />
                      </div>
                      <div className="text-xl font-bold text-white">68%</div>
                      <div className="text-xs text-[#10B981]">+5% this week</div>
                    </div>
                  </div>

                  {/* Content Performance */}
                  <div>
                    <h4 className="text-sm font-medium text-white mb-3">Content Performance</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-[#94A3B8]">Published Content</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-24 h-2 bg-[#334155] rounded-full overflow-hidden">
                            <div className="h-full bg-[#10B981] rounded-full" style={{ width: '75%' }}></div>
                          </div>
                          <span className="text-sm text-white">75%</span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-[#94A3B8]">Draft Content</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-24 h-2 bg-[#334155] rounded-full overflow-hidden">
                            <div className="h-full bg-[#F59E0B] rounded-full" style={{ width: '25%' }}></div>
                          </div>
                          <span className="text-sm text-white">25%</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Recent Activity */}
                  <div>
                    <h4 className="text-sm font-medium text-white mb-3">Recent Activity</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-3 text-sm">
                        <div className="w-2 h-2 bg-[#10B981] rounded-full"></div>
                        <span className="text-[#94A3B8]">New blog published</span>
                        <span className="text-[#64748B]">2 hours ago</span>
                      </div>
                      <div className="flex items-center space-x-3 text-sm">
                        <div className="w-2 h-2 bg-[#3B82F6] rounded-full"></div>
                        <span className="text-[#94A3B8]">Media uploaded</span>
                        <span className="text-[#64748B]">4 hours ago</span>
                      </div>
                      <div className="flex items-center space-x-3 text-sm">
                        <div className="w-2 h-2 bg-[#F59E0B] rounded-full"></div>
                        <span className="text-[#94A3B8]">Website updated</span>
                        <span className="text-[#64748B]">1 day ago</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Enhanced Analytics Section */}
        <div className="mt-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Detailed Analytics</h2>
            <Button variant="outline" className="border-[#475569] text-[#CBD5E1] hover:bg-[#334155] hover:text-white">
              <Calendar className="h-4 w-4 mr-2" />
              Last 30 Days
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Traffic Sources */}
            <Card className="card-dark">
              <CardHeader className="border-b border-[#334155]">
                <CardTitle className="text-white">Traffic Sources</CardTitle>
                <CardDescription className="text-[#94A3B8]">Where your visitors come from</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-[#3B82F6] rounded-full"></div>
                      <span className="text-[#CBD5E1]">Direct</span>
                    </div>
                    <div className="text-right">
                      <div className="text-white font-medium">45%</div>
                      <div className="text-xs text-[#94A3B8]">2,340 visits</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-[#10B981] rounded-full"></div>
                      <span className="text-[#CBD5E1]">Search Engines</span>
                    </div>
                    <div className="text-right">
                      <div className="text-white font-medium">32%</div>
                      <div className="text-xs text-[#94A3B8]">1,670 visits</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-[#F59E0B] rounded-full"></div>
                      <span className="text-[#CBD5E1]">Social Media</span>
                    </div>
                    <div className="text-right">
                      <div className="text-white font-medium">18%</div>
                      <div className="text-xs text-[#94A3B8]">940 visits</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-[#8B5CF6] rounded-full"></div>
                      <span className="text-[#CBD5E1]">Referrals</span>
                    </div>
                    <div className="text-right">
                      <div className="text-white font-medium">5%</div>
                      <div className="text-xs text-[#94A3B8]">260 visits</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Top Performing Content */}
            <Card className="card-dark">
              <CardHeader className="border-b border-[#334155]">
                <CardTitle className="text-white">Top Performing Content</CardTitle>
                <CardDescription className="text-[#94A3B8]">Most viewed blog posts</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {recentBlogs?.data?.slice(0, 4).map((blog: any, index: number) => (
                    <div key={blog._id} className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-[#3B82F6] to-[#1E40AF] rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">{index + 1}</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-[#CBD5E1] text-sm font-medium truncate">{blog.title}</p>
                        <p className="text-[#94A3B8] text-xs">{blog.viewCount || 0} views</p>
                      </div>
                      <div className="flex items-center text-xs text-[#10B981]">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        +{Math.floor(Math.random() * 20) + 5}%
                      </div>
                    </div>
                  )) || (
                    <div className="text-center py-4">
                      <p className="text-[#94A3B8] text-sm">No blog data available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
