'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { 
  Image as ImageIcon, 
  Upload, 
  Search, 
  Filter,
  Grid3X3,
  List,
  Download,
  Trash2,
  Edit,
  Copy,
  Eye,
  FileText,
  File,
  Video,
  Music,
  Archive,
  MoreHorizontal,
  X,
  Check
} from 'lucide-react';
import { useMedia, useUploadMedia, useDeleteMedia, useBulkDeleteMedia } from '@/hooks/use-media';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { formatFileSize, formatDate, formatRelativeTime } from '@/lib/utils';
import toast from 'react-hot-toast';

interface MediaItemProps {
  media: any;
  isSelected: boolean;
  onSelect: (id: string) => void;
  viewMode: 'grid' | 'list';
}

function MediaItem({ media, isSelected, onSelect, viewMode }: MediaItemProps) {
  const deleteMedia = useDeleteMedia();

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return ImageIcon;
    if (type.startsWith('video/')) return Video;
    if (type.startsWith('audio/')) return Music;
    if (type.includes('pdf') || type.includes('document')) return FileText;
    if (type.includes('zip') || type.includes('rar')) return Archive;
    return File;
  };

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(media.url);
      toast.success('URL copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy URL');
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this media file?')) {
      deleteMedia.mutate(media._id);
    }
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = media.url;
    link.download = media.originalName;
    link.click();
  };

  if (viewMode === 'list') {
    return (
      <div className={`flex items-center p-4 border rounded-lg hover:bg-gray-50 ${
        isSelected ? 'bg-blue-50 border-blue-200' : 'bg-white border-gray-200'
      }`}>
        <div className="flex items-center space-x-3 flex-1">
          <div className="relative">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => onSelect(media._id)}
              className="absolute top-2 left-2 z-10"
            />
            {media.type.startsWith('image/') ? (
              <img
                src={media.url}
                alt={media.alt || media.originalName}
                className="w-16 h-16 object-cover rounded"
              />
            ) : (
              <div className="w-16 h-16 bg-gray-100 rounded flex items-center justify-center">
                {(() => {
                  const Icon = getFileIcon(media.type);
                  return <Icon className="h-8 w-8 text-gray-400" />;
                })()}
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-gray-900 truncate">
              {media.originalName}
            </h3>
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <span>{formatFileSize(media.size)}</span>
              <span>{media.type}</span>
              <span>{formatRelativeTime(media.createdAt)}</span>
            </div>
            {media.alt && (
              <p className="text-xs text-gray-600 truncate mt-1">{media.alt}</p>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={handleCopyUrl}>
            <Copy className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={handleDownload}>
            <Download className="h-4 w-4" />
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleDelete}
            disabled={deleteMedia.isPending}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Card className={`hover:shadow-md transition-shadow ${
      isSelected ? 'ring-2 ring-blue-500' : ''
    }`}>
      <CardContent className="p-4">
        <div className="relative">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onSelect(media._id)}
            className="absolute top-2 left-2 z-10"
          />
          {media.type.startsWith('image/') ? (
            <img
              src={media.url}
              alt={media.alt || media.originalName}
              className="w-full h-32 object-cover rounded mb-3"
            />
          ) : (
            <div className="w-full h-32 bg-gray-100 rounded mb-3 flex items-center justify-center">
              {(() => {
                const Icon = getFileIcon(media.type);
                return <Icon className="h-12 w-12 text-gray-400" />;
              })()}
            </div>
          )}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-900 truncate">
              {media.originalName}
            </h3>
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>{formatFileSize(media.size)}</span>
              <span>{formatRelativeTime(media.createdAt)}</span>
            </div>
            {media.alt && (
              <p className="text-xs text-gray-600 truncate">{media.alt}</p>
            )}
            <div className="flex items-center space-x-1 pt-2">
              <Button variant="ghost" size="sm" onClick={handleCopyUrl}>
                <Copy className="h-3 w-3" />
              </Button>
              <Button variant="ghost" size="sm" onClick={handleDownload}>
                <Download className="h-3 w-3" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={handleDelete}
                disabled={deleteMedia.isPending}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function MediaSkeleton({ viewMode }: { viewMode: 'grid' | 'list' }) {
  if (viewMode === 'list') {
    return (
      <div className="flex items-center p-4 border rounded-lg bg-white">
        <div className="flex items-center space-x-3 flex-1">
          <Skeleton className="w-16 h-16 rounded" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardContent className="p-4">
        <Skeleton className="w-full h-32 rounded mb-3" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <div className="flex justify-between">
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-3 w-20" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function MediaPage() {
  const [search, setSearch] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const { data: media, isLoading } = useMedia({ search, type: typeFilter });
  const uploadMedia = useUploadMedia();
  const bulkDeleteMedia = useBulkDeleteMedia();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setIsUploading(true);
    let successCount = 0;
    let errorCount = 0;

    try {
      for (const file of acceptedFiles) {
        try {
          const formData = new FormData();
          formData.append('file', file);
          formData.append('folder', 'uploads');
          await uploadMedia.mutateAsync(formData);
          successCount++;
        } catch (error) {
          errorCount++;
          console.error(`Failed to upload ${file.name}:`, error);
        }
      }

      // Show summary message
      if (successCount > 0 && errorCount === 0) {
        toast.success(`Successfully uploaded ${successCount} file${successCount > 1 ? 's' : ''}`);
      } else if (successCount > 0 && errorCount > 0) {
        toast.success(`Uploaded ${successCount} file${successCount > 1 ? 's' : ''}, ${errorCount} failed`);
      } else if (errorCount > 0) {
        toast.error(`Failed to upload ${errorCount} file${errorCount > 1 ? 's' : ''}`);
      }
    } catch (error) {
      toast.error('Upload failed');
    } finally {
      setIsUploading(false);
    }
  }, [uploadMedia]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
      'video/*': ['.mp4', '.mov', '.avi', '.mkv'],
      'audio/*': ['.mp3', '.wav', '.ogg'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const handleSelectItem = (id: string) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === media?.media?.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(media?.media?.map((item: any) => item._id) || []);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedItems.length} media files?`)) {
      try {
        await bulkDeleteMedia.mutateAsync(selectedItems);
        setSelectedItems([]);
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  const filteredMedia = media?.media?.filter((item: any) => {
    if (typeFilter) {
      if (typeFilter === 'images' && !item.type.startsWith('image/')) return false;
      if (typeFilter === 'videos' && !item.type.startsWith('video/')) return false;
      if (typeFilter === 'audio' && !item.type.startsWith('audio/')) return false;
      if (typeFilter === 'documents' && !item.type.includes('pdf') && !item.type.includes('document')) return false;
    }
    return true;
  }) || [];

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 flex items-center">
              <ImageIcon className="h-8 w-8 mr-3 text-[#3B82F6]" />
              <span className="bg-gradient-to-r from-[#3B82F6] to-[#60A5FA] bg-clip-text text-transparent">
                Media Library
              </span>
            </h1>
            <p className="text-[#CBD5E1] text-lg">
              Manage your media files and assets
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {selectedItems.length > 0 && (
              <Button
                variant="destructive"
                onClick={handleBulkDelete}
                disabled={bulkDeleteMedia.isPending}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selected ({selectedItems.length})
              </Button>
            )}
          </div>
        </div>

        {/* Upload Area */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-blue-400 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              {isDragActive ? (
                <p className="text-lg text-blue-600">Drop the files here...</p>
              ) : (
                <div>
                  <p className="text-lg text-gray-600 mb-2">
                    Drag & drop files here, or click to select files
                  </p>
                  <p className="text-sm text-gray-500">
                    Supports images, videos, audio, and documents up to 10MB
                  </p>
                </div>
              )}
              {isUploading && (
                <div className="mt-4">
                  <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    Uploading...
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Filters and Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search media..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10"
            />
          </div>

          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All types</option>
            <option value="images">Images</option>
            <option value="videos">Videos</option>
            <option value="audio">Audio</option>
            <option value="documents">Documents</option>
          </select>

          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>

          {media?.media?.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
            >
              {selectedItems.length === media.media.length ? (
                <>
                  <X className="h-4 w-4 mr-2" />
                  Deselect All
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Select All
                </>
              )}
            </Button>
          )}
        </div>

        {/* Stats */}
        {media && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">{media.total}</div>
                <p className="text-xs text-muted-foreground">Total files</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">
                  {media.media?.filter((m: any) => m.type.startsWith('image/')).length || 0}
                </div>
                <p className="text-xs text-muted-foreground">Images</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">
                  {formatFileSize(media.totalSize || 0)}
                </div>
                <p className="text-xs text-muted-foreground">Total size</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">{selectedItems.length}</div>
                <p className="text-xs text-muted-foreground">Selected</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Media Grid/List */}
        {isLoading ? (
          <div className={viewMode === 'grid'
            ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4'
            : 'space-y-2'
          }>
            {[...Array(12)].map((_, i) => (
              <MediaSkeleton key={i} viewMode={viewMode} />
            ))}
          </div>
        ) : filteredMedia.length > 0 ? (
          <div className={viewMode === 'grid'
            ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4'
            : 'space-y-2'
          }>
            {filteredMedia.map((item: any) => (
              <MediaItem
                key={item._id}
                media={item}
                isSelected={selectedItems.includes(item._id)}
                onSelect={handleSelectItem}
                viewMode={viewMode}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No media files found</h3>
            <p className="text-gray-600 mb-4">
              {search || typeFilter
                ? 'No media files match your current filters.'
                : 'Upload your first media file to get started.'}
            </p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
