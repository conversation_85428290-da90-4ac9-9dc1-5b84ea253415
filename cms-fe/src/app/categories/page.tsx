'use client';

import { useState } from 'react';
import { Plus, Search, Edit, Trash2, Tag, X, Save } from 'lucide-react';
import { useCategories, useCreateCategory, useUpdateCategory, useDeleteCategory } from '@/hooks/use-categories';
import { useMyWebsites } from '@/hooks/use-dashboard';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import toast from 'react-hot-toast';

interface Category {
  _id: string;
  name: string;
  slug: string;
  description: string;
  postCount: number;
  color: string;
}

// Initial categories data
const initialCategories: Category[] = [
  { _id: '1', name: 'Technology', slug: 'technology', description: 'Tech-related content', postCount: 15, color: '#3B82F6' },
  { _id: '2', name: 'Business', slug: 'business', description: 'Business and entrepreneurship', postCount: 8, color: '#10B981' },
  { _id: '3', name: 'Lifestyle', slug: 'lifestyle', description: 'Lifestyle and personal development', postCount: 12, color: '#F59E0B' },
  { _id: '4', name: 'Travel', slug: 'travel', description: 'Travel guides and experiences', postCount: 6, color: '#EF4444' },
];

function CategoryCard({
  category,
  onEdit,
  onDelete
}: {
  category: Category;
  onEdit: (category: Category) => void;
  onDelete: (id: string) => void;
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    name: category.name,
    description: category.description,
    color: category.color,
  });

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    const updatedCategory = {
      ...category,
      ...editData,
      slug: editData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
    };
    onEdit(updatedCategory);
    setIsEditing(false);
    toast.success('Category updated successfully!');
  };

  const handleCancel = () => {
    setEditData({
      name: category.name,
      description: category.description,
      color: category.color,
    });
    setIsEditing(false);
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      onDelete(category._id);
      toast.success('Category deleted successfully!');
    }
  };

  return (
    <Card className="card-dark hover:border-[#475569] transition-all duration-200">
      <CardHeader className="border-b border-[#334155]">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3 flex-1">
            {isEditing ? (
              <input
                type="color"
                value={editData.color}
                onChange={(e) => setEditData(prev => ({ ...prev, color: e.target.value }))}
                className="w-8 h-8 rounded border-none cursor-pointer"
              />
            ) : (
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: category.color }}
              />
            )}
            <div className="flex-1">
              {isEditing ? (
                <div className="space-y-2">
                  <Input
                    value={editData.name}
                    onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                    className="text-lg font-semibold bg-[#1E293B] border-[#475569] text-white"
                    placeholder="Category name"
                  />
                  <Input
                    value={editData.description}
                    onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                    className="text-sm bg-[#1E293B] border-[#475569] text-[#94A3B8]"
                    placeholder="Category description"
                  />
                </div>
              ) : (
                <>
                  <CardTitle className="text-white text-lg">{category.name}</CardTitle>
                  <CardDescription className="text-[#94A3B8]">
                    /{category.slug}
                  </CardDescription>
                </>
              )}
            </div>
          </div>
          <Badge variant="outline" className="border-[#334155] text-[#CBD5E1]">
            {category.postCount} posts
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-4">
          {!isEditing && (
            <p className="text-[#94A3B8] text-sm">{category.description}</p>
          )}

          <div className="flex items-center justify-between pt-4 border-t border-[#334155]">
            {isEditing ? (
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSave}
                  className="text-[#10B981] hover:text-white hover:bg-[#10B981]"
                >
                  <Save className="h-4 w-4 mr-1" />
                  Save
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCancel}
                  className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
                >
                  <X className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleEdit}
                  className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              </div>
            )}
            {!isEditing && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDelete}
                className="text-[#CBD5E1] hover:text-[#EF4444] hover:bg-[#334155]"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function CategoriesPage() {
  const [search, setSearch] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newCategory, setNewCategory] = useState({
    name: '',
    description: '',
    color: '#3B82F6',
    websiteId: '',
  });

  const { data: categoriesData, isLoading } = useCategories({ search });
  const { data: websites } = useMyWebsites();
  const createCategory = useCreateCategory();
  const updateCategory = useUpdateCategory();
  const deleteCategory = useDeleteCategory();

  const categories = categoriesData?.data || [];
  const websitesList = websites?.data || [];

  const filteredCategories = categories;

  const handleCreateCategory = () => {
    setShowCreateForm(true);
  };

  const handleSaveNewCategory = async () => {
    if (!newCategory.name.trim()) {
      toast.error('Category name is required');
      return;
    }

    if (!newCategory.websiteId) {
      toast.error('Please select a website');
      return;
    }

    try {
      await createCategory.mutateAsync({
        name: newCategory.name,
        description: newCategory.description,
        color: newCategory.color,
        websiteId: newCategory.websiteId,
      });
      setNewCategory({ name: '', description: '', color: '#3B82F6', websiteId: '' });
      setShowCreateForm(false);
    } catch (error) {
      // Error handled by the mutation
    }
  };

  const handleEditCategory = async (updatedCategory: Category) => {
    try {
      await updateCategory.mutateAsync({
        id: updatedCategory._id,
        data: {
          name: updatedCategory.name,
          description: updatedCategory.description,
          color: updatedCategory.color,
        },
      });
    } catch (error) {
      // Error handled by the mutation
    }
  };

  const handleDeleteCategory = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      try {
        await deleteCategory.mutateAsync(id);
      } catch (error) {
        // Error handled by the mutation
      }
    }
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 flex items-center">
              <Tag className="h-8 w-8 mr-3 text-[#3B82F6]" />
              <span className="bg-gradient-to-r from-[#3B82F6] to-[#60A5FA] bg-clip-text text-transparent">
                Categories
              </span>
            </h1>
            <p className="text-[#CBD5E1] text-lg">
              Organize your content with categories
            </p>
          </div>
          <Button onClick={handleCreateCategory} className="btn-primary">
            <Plus className="h-4 w-4 mr-2" />
            Create Category
          </Button>
        </div>

        {/* Search */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#94A3B8]" />
            <Input
              placeholder="Search categories..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 input-dark"
            />
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="card-dark">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Tag className="h-8 w-8 text-[#3B82F6]" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-[#94A3B8]">Total Categories</p>
                  <p className="text-2xl font-bold text-white">{categories.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="card-dark">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Edit className="h-8 w-8 text-[#10B981]" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-[#94A3B8]">Total Posts</p>
                  <p className="text-2xl font-bold text-white">
                    {categories.reduce((sum: number, cat: any) => sum + (cat.postCount || 0), 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="card-dark">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Tag className="h-8 w-8 text-[#F59E0B]" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-[#94A3B8]">Most Popular</p>
                  <p className="text-lg font-bold text-white">
                    {categories.length > 0 ? categories.reduce((max: any, cat: any) => (cat.postCount || 0) > (max.postCount || 0) ? cat : max, categories[0])?.name : 'N/A'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="card-dark">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Search className="h-8 w-8 text-[#8B5CF6]" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-[#94A3B8]">Avg Posts/Category</p>
                  <p className="text-2xl font-bold text-white">
                    {categories.length > 0 ? Math.round(categories.reduce((sum: number, cat: any) => sum + (cat.postCount || 0), 0) / categories.length) : 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Create Category Form */}
        {showCreateForm && (
          <Card className="card-dark mb-6">
            <CardHeader className="border-b border-[#334155]">
              <CardTitle className="text-white">Create New Category</CardTitle>
              <CardDescription className="text-[#94A3B8]">Add a new category to organize your content</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-2">Name *</label>
                  <Input
                    value={newCategory.name}
                    onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Category name"
                    className="bg-[#1E293B] border-[#475569] text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-2">Website *</label>
                  <Select
                    value={newCategory.websiteId}
                    onValueChange={(value) => setNewCategory(prev => ({ ...prev, websiteId: value }))}
                  >
                    <SelectTrigger className="bg-[#1E293B] border-[#475569] text-white">
                      <SelectValue placeholder="Select website" />
                    </SelectTrigger>
                    <SelectContent>
                      {websitesList.map((website: any) => (
                        <SelectItem key={website._id} value={website._id}>
                          {website.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-2">Color</label>
                  <input
                    type="color"
                    value={newCategory.color}
                    onChange={(e) => setNewCategory(prev => ({ ...prev, color: e.target.value }))}
                    className="w-full h-10 rounded border border-[#475569] cursor-pointer"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-white mb-2">Description</label>
                  <Input
                    value={newCategory.description}
                    onChange={(e) => setNewCategory(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Category description"
                    className="bg-[#1E293B] border-[#475569] text-white"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2 mt-6">
                <Button onClick={handleSaveNewCategory} className="btn-primary">
                  <Save className="h-4 w-4 mr-2" />
                  Create Category
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowCreateForm(false)}
                  className="border-[#475569] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Categories Grid */}
        {filteredCategories.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCategories.map((category: any) => (
              <CategoryCard
                key={category._id}
                category={category}
                onEdit={handleEditCategory}
                onDelete={handleDeleteCategory}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gradient-to-br from-[#3B82F6] to-[#1E40AF] rounded-full flex items-center justify-center mx-auto mb-4">
              <Tag className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">No categories found</h3>
            <p className="text-[#94A3B8] mb-6">
              {search ? 'No categories match your search criteria.' : 'Get started by creating your first category.'}
            </p>
            <Button onClick={handleCreateCategory} className="btn-primary">
              <Plus className="h-4 w-4 mr-2" />
              Create Category
            </Button>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
