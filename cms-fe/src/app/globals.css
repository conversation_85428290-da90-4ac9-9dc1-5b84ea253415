@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dark Theme Design System */
:root {
  /* Primary Brand Colors */
  --primary-blue: #1E40AF;
  --primary-blue-light: #3B82F6;
  --primary-blue-dark: #1E3A8A;

  /* Dark Theme Colors */
  --bg-primary: #0F0F23;
  --bg-secondary: #1A1A2E;
  --bg-tertiary: #16213E;
  --bg-card: #1E293B;
  --bg-hover: #334155;

  /* Text Colors - Improved for better readability */
  --text-primary: #FFFFFF;
  --text-secondary: #E2E8F0;
  --text-muted: #CBD5E1;
  --text-accent: #60A5FA;
  --text-high-contrast: #F1F5F9;

  /* Border Colors */
  --border-primary: #334155;
  --border-secondary: #475569;
  --border-accent: #3B82F6;

  /* Status Colors */
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;
}

/* Base Styles */
* {
  border-color: var(--border-primary);
}

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Custom Components */
.glass-effect {
  background: rgba(30, 41, 59, 0.9);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.gradient-border {
  position: relative;
  background: var(--bg-card);
  border-radius: 12px;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-light));
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-light));
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.card-dark {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.card-dark:hover {
  border-color: var(--border-accent);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
}

.input-dark {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.input-dark:focus {
  border-color: var(--border-accent);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.sidebar-dark {
  background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  border-right: 1px solid var(--border-primary);
}

.nav-item {
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 2px 0;
}

.nav-item:hover {
  background: var(--bg-hover);
  color: var(--text-accent);
}

.nav-item.active {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-light));
  color: white;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

/* Status Indicators */
.status-success {
  background: var(--success);
  color: white;
}

.status-warning {
  background: var(--warning);
  color: white;
}

.status-error {
  background: var(--error);
  color: white;
}

.status-info {
  background: var(--info);
  color: white;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Loading animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* TipTap Editor Styles */
.ProseMirror {
  @apply outline-none min-h-[200px] p-4 prose prose-sm max-w-none;
  color: var(--text-primary) !important;
}

.ProseMirror p.is-editor-empty:first-child::before {
  @apply text-muted-foreground;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Improved text contrast classes */
.text-high-contrast {
  color: var(--text-high-contrast) !important;
}

.text-readable {
  color: var(--text-secondary) !important;
}

/* Form field improvements */
.form-label {
  color: var(--text-high-contrast) !important;
  font-weight: 500;
}

.form-description {
  color: var(--text-secondary) !important;
}

.form-error {
  color: var(--error) !important;
  font-weight: 500;
}

/* Dropdown and select improvements */
.dropdown-item {
  color: var(--text-primary) !important;
}

.dropdown-item:hover {
  background-color: var(--bg-hover) !important;
  color: var(--text-high-contrast) !important;
}

/* Icon container fixes */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;
}

.icon-container svg {
  flex-shrink: 0;
}

/* Card content overflow fixes */
.card-content {
  overflow: hidden;
}

.card-content * {
  max-width: 100%;
}

/* Button icon fixes */
.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.btn-icon svg {
  flex-shrink: 0;
}

/* Prevent text and icon overflow */
.text-container {
  min-width: 0;
  flex: 1;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
