'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  FileText, 
  Plus, 
  Search, 
  Filter,
  Edit, 
  Trash2, 
  Eye,
  Calendar,
  Share2,
  MoreHorizontal
} from 'lucide-react';
import { useBlogs, useDeleteBlog, usePublishBlog, useUpdateBlog, useScheduleBlog } from '@/hooks/use-blogs';
import { useMyWebsites } from '@/hooks/use-dashboard';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate, formatRelativeTime, truncateText } from '@/lib/utils';
import toast from 'react-hot-toast';

function BlogCard({
  blog,
  isSelected,
  onSelect
}: {
  blog: any;
  isSelected: boolean;
  onSelect: (blogId: string) => void;
}) {
  const router = useRouter();
  const [isQuickEditing, setIsQuickEditing] = useState(false);
  const [quickEditData, setQuickEditData] = useState({
    title: blog.title,
    excerpt: blog.excerpt || '',
  });
  const deleteBlog = useDeleteBlog();
  const publishBlog = usePublishBlog();
  const updateBlog = useUpdateBlog();
  const scheduleBlog = useScheduleBlog();

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this blog? This action cannot be undone.')) {
      deleteBlog.mutate(blog._id);
    }
  };

  const handlePublish = async () => {
    if (window.confirm('Are you sure you want to publish this blog?')) {
      publishBlog.mutate(blog._id);
    }
  };

  const handleSchedule = async () => {
    const scheduledAt = prompt('Enter schedule date and time (YYYY-MM-DDTHH:MM):');
    if (scheduledAt) {
      try {
        const date = new Date(scheduledAt);
        if (date > new Date()) {
          scheduleBlog.mutate({ id: blog._id, scheduledAt: date.toISOString() });
        } else {
          toast.error('Schedule date must be in the future');
        }
      } catch (error) {
        toast.error('Invalid date format');
      }
    }
  };

  const handleQuickEdit = () => {
    setIsQuickEditing(true);
    setQuickEditData({
      title: blog.title,
      excerpt: blog.excerpt || '',
    });
  };

  const handleQuickSave = async () => {
    try {
      await updateBlog.mutateAsync({
        id: blog._id,
        data: quickEditData,
      });
      setIsQuickEditing(false);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleQuickCancel = () => {
    setIsQuickEditing(false);
    setQuickEditData({
      title: blog.title,
      excerpt: blog.excerpt || '',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'success';
      case 'draft':
        return 'secondary';
      case 'scheduled':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  return (
    <Card className="card-dark group hover:scale-[1.02] transition-all duration-300">
      <CardHeader className="border-b border-[#334155]">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => onSelect(blog._id)}
              className="rounded border-[#475569] bg-[#1E293B] text-[#3B82F6] focus:ring-[#3B82F6]"
              onClick={(e) => e.stopPropagation()}
            />
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-3">
              <Badge variant={getStatusColor(blog.status)}>
                {blog.status}
              </Badge>
              {blog.isFeatured && (
                <Badge variant="outline" className="border-[#F59E0B] text-[#F59E0B]">
                  ⭐ Featured
                </Badge>
              )}
            </div>
            {isQuickEditing ? (
              <div className="space-y-3">
                <Input
                  value={quickEditData.title}
                  onChange={(e) => setQuickEditData(prev => ({ ...prev, title: e.target.value }))}
                  className="text-lg font-semibold bg-[#1E293B] border-[#475569] text-white"
                  placeholder="Blog title"
                />
                <textarea
                  value={quickEditData.excerpt}
                  onChange={(e) => setQuickEditData(prev => ({ ...prev, excerpt: e.target.value }))}
                  className="w-full p-2 text-sm bg-[#1E293B] border border-[#475569] rounded-md text-[#94A3B8] resize-none"
                  placeholder="Blog excerpt"
                  rows={2}
                />
              </div>
            ) : (
              <>
                <CardTitle className="text-lg text-white group-hover:text-[#60A5FA] transition-colors line-clamp-2 mb-2">
                  {blog.title}
                </CardTitle>
                <CardDescription className="text-[#94A3B8] line-clamp-2">
                  {blog.excerpt}
                </CardDescription>
              </>
            )}
            </div>
          </div>
          {blog.featuredImage && (
            <img
              src={blog.featuredImage}
              alt={blog.title}
              className="w-16 h-16 object-cover rounded-lg ml-4 border border-[#334155]"
            />
          )}
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-[#94A3B8]">Website: <span className="text-[#CBD5E1]">{blog.websiteId?.name || 'Unknown'}</span></span>
            <span className="text-[#94A3B8]">{formatRelativeTime(blog.createdAt)}</span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-[#94A3B8]">Author: <span className="text-[#CBD5E1]">{blog.authorId?.firstName} {blog.authorId?.lastName}</span></span>
            <span className="text-[#94A3B8]">Updated: {formatRelativeTime(blog.updatedAt)}</span>
          </div>

          <div className="flex items-center space-x-6 text-sm text-[#94A3B8]">
            <div className="flex items-center">
              <Eye className="h-4 w-4 mr-1 text-[#3B82F6]" />
              <span className="text-[#CBD5E1]">{blog.viewCount}</span>
            </div>
            <div className="flex items-center">
              <Share2 className="h-4 w-4 mr-1 text-[#3B82F6]" />
              <span className="text-[#CBD5E1]">{blog.shareCount}</span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1 text-[#3B82F6]" />
              <span className="text-[#CBD5E1]">{blog.readingTime} min read</span>
            </div>
          </div>

          {blog.categories?.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {blog.categories.slice(0, 3).map((category: string) => (
                <Badge key={category} variant="outline" className="text-xs border-[#334155] text-[#CBD5E1]">
                  {category}
                </Badge>
              ))}
              {blog.categories.length > 3 && (
                <Badge variant="outline" className="text-xs border-[#334155] text-[#CBD5E1]">
                  +{blog.categories.length - 3} more
                </Badge>
              )}
            </div>
          )}

          <div className="flex items-center justify-between pt-4 border-t border-[#334155]">
            {isQuickEditing ? (
              <div className="flex items-center space-x-2 w-full">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleQuickSave}
                  disabled={updateBlog.isPending}
                  className="text-[#10B981] hover:text-white hover:bg-[#10B981]"
                >
                  Save
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleQuickCancel}
                  className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleQuickEdit}
                    className="text-[#3B82F6] hover:text-white hover:bg-[#3B82F6]"
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Quick Edit
                  </Button>
                  <Link href={`/blogs/${blog._id}/edit`}>
                    <Button variant="ghost" size="sm" className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]">
                      <Edit className="h-4 w-4 mr-1" />
                      Full Edit
                    </Button>
                  </Link>
                  {blog.status === 'draft' && (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handlePublish}
                        disabled={publishBlog.isPending}
                        className="text-[#10B981] hover:text-white hover:bg-[#10B981]"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Publish
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleSchedule}
                        disabled={scheduleBlog.isPending}
                        className="text-[#F59E0B] hover:text-white hover:bg-[#F59E0B]"
                      >
                        <Calendar className="h-4 w-4 mr-1" />
                        Schedule
                      </Button>
                    </>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDelete}
                  disabled={deleteBlog.isPending}
                  className="text-[#CBD5E1] hover:text-[#EF4444] hover:bg-[#334155]"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function BlogCardSkeleton() {
  return (
    <Card className="card-dark animate-pulse">
      <CardHeader className="border-b border-[#334155]">
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-3">
            <div className="flex items-center space-x-2">
              <Skeleton className="h-5 w-16 bg-[#334155]" />
              <Skeleton className="h-5 w-16 bg-[#334155]" />
            </div>
            <Skeleton className="h-6 w-3/4 bg-[#334155]" />
            <Skeleton className="h-4 w-full bg-[#334155]" />
          </div>
          <Skeleton className="w-16 h-16 rounded-lg bg-[#334155]" />
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="flex justify-between">
            <Skeleton className="h-4 w-24 bg-[#334155]" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex space-x-4">
            <Skeleton className="h-4 w-12" />
            <Skeleton className="h-4 w-12" />
            <Skeleton className="h-4 w-16" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-5 w-20" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function BlogsPage() {
  const searchParams = useSearchParams();
  const preselectedWebsiteId = searchParams.get('websiteId');
  
  const [search, setSearch] = useState('');
  const [selectedWebsite, setSelectedWebsite] = useState(preselectedWebsiteId || '');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedBlogs, setSelectedBlogs] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);

  const { data: blogs, isLoading } = useBlogs({ 
    search, 
    websiteId: selectedWebsite || undefined 
  });
  const { data: websites } = useMyWebsites();

  const filteredBlogs = blogs?.data?.filter((blog: any) => {
    if (statusFilter && blog.status !== statusFilter) return false;
    return true;
  }) || [];

  const handleSelectAll = () => {
    if (selectedBlogs.length === filteredBlogs.length) {
      setSelectedBlogs([]);
    } else {
      setSelectedBlogs(filteredBlogs.map((blog: any) => blog._id));
    }
  };

  const handleSelectBlog = (blogId: string) => {
    setSelectedBlogs(prev =>
      prev.includes(blogId)
        ? prev.filter(id => id !== blogId)
        : [...prev, blogId]
    );
  };

  const handleBulkDelete = async () => {
    if (window.confirm(`Are you sure you want to delete ${selectedBlogs.length} blog(s)?`)) {
      // Implement bulk delete
      toast.success(`${selectedBlogs.length} blog(s) deleted successfully!`);
      setSelectedBlogs([]);
    }
  };

  const handleBulkPublish = async () => {
    if (window.confirm(`Are you sure you want to publish ${selectedBlogs.length} blog(s)?`)) {
      // Implement bulk publish
      toast.success(`${selectedBlogs.length} blog(s) published successfully!`);
      setSelectedBlogs([]);
    }
  };

  const handleBulkUnpublish = async () => {
    if (window.confirm(`Are you sure you want to unpublish ${selectedBlogs.length} blog(s)?`)) {
      // Implement bulk unpublish
      toast.success(`${selectedBlogs.length} blog(s) unpublished successfully!`);
      setSelectedBlogs([]);
    }
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 flex items-center">
              <FileText className="h-8 w-8 mr-3 text-[#3B82F6]" />
              <span className="bg-gradient-to-r from-[#3B82F6] to-[#60A5FA] bg-clip-text text-transparent">
                Blogs
              </span>
            </h1>
            <p className="text-[#CBD5E1] text-lg">
              Manage your blog posts and content
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Link href="/blogs/trash">
              <Button
                variant="outline"
                className="border-[#475569] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Trash
              </Button>
            </Link>
            <Link href="/blogs/new">
              <Button className="btn-primary">
                <Plus className="h-4 w-4 mr-2" />
                Create Blog
              </Button>
            </Link>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#94A3B8]" />
            <Input
              placeholder="Search blogs..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 input-dark"
            />
          </div>
          
          <select
            value={selectedWebsite}
            onChange={(e) => setSelectedWebsite(e.target.value)}
            className="px-4 py-3 border border-[#334155] bg-[#1A1A2E] text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-[#3B82F6] transition-all"
          >
            <option value="">All websites</option>
            {websites?.data?.map((website: any) => (
              <option key={website._id} value={website._id}>
                {website.name}
              </option>
            ))}
          </select>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-3 border border-[#334155] bg-[#1A1A2E] text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-[#3B82F6] transition-all"
          >
            <option value="">All statuses</option>
            <option value="draft">Draft</option>
            <option value="published">Published</option>
            <option value="scheduled">Scheduled</option>
          </select>
        </div>

        {/* Bulk Actions */}
        {selectedBlogs.length > 0 && (
          <div className="bg-[#1E293B] border border-[#334155] rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-white font-medium">
                  {selectedBlogs.length} blog(s) selected
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedBlogs([])}
                  className="text-[#94A3B8] hover:text-white"
                >
                  Clear selection
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBulkPublish}
                  className="text-[#10B981] hover:text-white hover:bg-[#10B981]"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  Publish
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBulkUnpublish}
                  className="text-[#F59E0B] hover:text-white hover:bg-[#F59E0B]"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Unpublish
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBulkDelete}
                  className="text-[#EF4444] hover:text-white hover:bg-[#EF4444]"
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Stats */}
        {blogs && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="card-dark">
              <CardContent className="p-6">
                <div className="text-2xl font-bold text-white">{blogs.total}</div>
                <p className="text-xs text-[#94A3B8]">Total blogs</p>
              </CardContent>
            </Card>
            <Card className="card-dark">
              <CardContent className="p-6">
                <div className="text-2xl font-bold text-white">
                  {blogs.data?.filter((b: any) => b.status === 'published').length || 0}
                </div>
                <p className="text-xs text-[#94A3B8]">Published</p>
              </CardContent>
            </Card>
            <Card className="card-dark">
              <CardContent className="p-6">
                <div className="text-2xl font-bold text-white">
                  {blogs.data?.filter((b: any) => b.status === 'draft').length || 0}
                </div>
                <p className="text-xs text-[#94A3B8]">Drafts</p>
              </CardContent>
            </Card>
            <Card className="card-dark">
              <CardContent className="p-6">
                <div className="text-2xl font-bold text-white">
                  {blogs.data?.reduce((sum: number, b: any) => sum + (b.viewCount || 0), 0) || 0}
                </div>
                <p className="text-xs text-[#94A3B8]">Total views</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Select All */}
        {filteredBlogs.length > 0 && (
          <div className="flex items-center space-x-2 mb-4">
            <input
              type="checkbox"
              checked={selectedBlogs.length === filteredBlogs.length && filteredBlogs.length > 0}
              onChange={handleSelectAll}
              className="rounded border-[#475569] bg-[#1E293B] text-[#3B82F6] focus:ring-[#3B82F6]"
            />
            <label className="text-[#CBD5E1] text-sm">
              Select all ({filteredBlogs.length} blogs)
            </label>
          </div>
        )}

        {/* Blogs Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <BlogCardSkeleton key={i} />
            ))}
          </div>
        ) : filteredBlogs.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredBlogs.map((blog: any) => (
              <BlogCard
                key={blog._id}
                blog={blog}
                isSelected={selectedBlogs.includes(blog._id)}
                onSelect={handleSelectBlog}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-gradient-to-br from-[#3B82F6] to-[#1E40AF] rounded-full flex items-center justify-center mx-auto mb-6">
              <FileText className="h-10 w-10 text-white" />
            </div>
            <h3 className="text-xl font-medium text-white mb-2">No blogs found</h3>
            <p className="text-[#94A3B8] mb-6 max-w-md mx-auto">
              {search || selectedWebsite || statusFilter
                ? 'No blogs match your current filters. Try adjusting your search criteria.'
                : 'Get started by creating your first blog post to share your content with the world.'}
            </p>
            <Link href="/blogs/new">
              <Button className="btn-primary">
                <Plus className="h-4 w-4 mr-2" />
                Create Blog
              </Button>
            </Link>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
