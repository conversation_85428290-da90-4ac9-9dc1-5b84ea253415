'use client';

import { useState, useEffect, use<PERSON>allback, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Loader2, Save, Eye, Upload, AlertCircle, CheckCircle2, Globe, Sparkles } from 'lucide-react';
import { useCreateBlog } from '@/hooks/use-blogs';
import { useMyWebsites } from '@/hooks/use-dashboard';
import { mediaApi } from '@/lib/api';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { RichTextEditor } from '@/components/editor/rich-text-editor';
import { MediaPicker } from '@/components/media/media-picker';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { generateSlug, calculateReadingTime, debounce } from '@/lib/utils';
import { generateExcerpt, shouldUpdateExcerpt } from '@/lib/utils/excerpt';
import toast from 'react-hot-toast';

const blogSchema = z.object({
  title: z.string().min(2, 'Title must be at least 2 characters'),
  slug: z.string()
    .min(2, 'Slug must be at least 2 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
    .refine((slug) => !slug.startsWith('-') && !slug.endsWith('-'), 'Slug cannot start or end with a hyphen'),
  content: z.string().min(10, 'Content must be at least 10 characters'),
  excerpt: z.string().optional(),
  websiteId: z.string().min(1, 'Please select a website'),
  featuredImage: z.string().url().optional().or(z.literal('')),
  status: z.enum(['draft', 'published', 'scheduled']).default('draft'),
  categories: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  scheduledAt: z.string().optional(),
  isFeatured: z.boolean().default(false),
  allowComments: z.boolean().default(true),
  seo: z.object({
    seoTitle: z.string().optional(),
    seoDescription: z.string().optional(),
    focusKeyword: z.string().optional(),
    canonicalUrl: z.string().url().optional().or(z.literal('')),
    ogTitle: z.string().optional(),
    ogDescription: z.string().optional(),
    ogImage: z.string().url().optional().or(z.literal('')),
    noIndex: z.boolean().default(false),
    noFollow: z.boolean().default(false),
  }).optional(),
});

type BlogForm = z.infer<typeof blogSchema>;

export default function NewBlogPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const preselectedWebsiteId = searchParams.get('websiteId');
  
  const createBlog = useCreateBlog();
  const { data: websitesData, isLoading: websitesLoading, error: websitesError, refetch: refetchWebsites } = useMyWebsites();
  
  const [categoriesInput, setCategoriesInput] = useState('');
  const [tagsInput, setTagsInput] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [autoGenerateSlug, setAutoGenerateSlug] = useState(true);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isImageUploading, setIsImageUploading] = useState(false);
  const [isMediaPickerOpen, setIsMediaPickerOpen] = useState(false);

  // Memoize websites data to handle different API response structures
  const websites = useMemo(() => {
    if (!websitesData) return [];
    
    // Handle different possible response structures
    let websitesList = [];
    if (Array.isArray(websitesData)) {
      websitesList = websitesData;
    } else if (websitesData.websites && Array.isArray(websitesData.websites)) {
      websitesList = websitesData.websites;
    } else if (websitesData.data && Array.isArray(websitesData.data)) {
      websitesList = websitesData.data;
    } else if (typeof websitesData === 'object' && websitesData !== null) {
      // If it's an object, try to find an array property
      const possibleArrays = Object.values(websitesData).filter(Array.isArray);
      if (possibleArrays.length > 0) {
        websitesList = possibleArrays[0] as any[];
      }
    }
    
    console.log('🌐 Websites available for selection:', websitesList.length, websitesList);
    return websitesList;
  }, [websitesData]);

  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    getValues,
    formState: { errors, isDirty },
  } = useForm<BlogForm>({
    resolver: zodResolver(blogSchema),
    defaultValues: {
      status: 'draft',
      categories: [],
      tags: [],
      isFeatured: false,
      allowComments: true,
      websiteId: preselectedWebsiteId || '',
      seo: {
        noIndex: false,
        noFollow: false,
      },
    },
  });

  const watchTitle = watch('title');
  const watchContent = watch('content');
  const watchSlug = watch('slug');
  const watchExcerpt = watch('excerpt');
  const watchWebsiteId = watch('websiteId');

  // Optimized slug generation with debounce
  const debouncedSlugGeneration = useCallback(
    debounce((title: string) => {
      if (autoGenerateSlug && title && title.length >= 2) {
        const newSlug = generateSlug(title);
        if (newSlug && newSlug.length >= 2) {
          setValue('slug', newSlug, { shouldValidate: false });
        }
      }
    }, 500),
    [autoGenerateSlug, setValue]
  );

  // Auto-generate slug from title
  useEffect(() => {
    if (watchTitle) {
      debouncedSlugGeneration(watchTitle);
    }
  }, [watchTitle, debouncedSlugGeneration]);

  // Disable auto-generation if user manually edits slug
  useEffect(() => {
    if (watchSlug && watchTitle && autoGenerateSlug) {
      const expectedSlug = generateSlug(watchTitle);
      if (watchSlug !== expectedSlug) {
        setAutoGenerateSlug(false);
      }
    }
  }, [watchSlug, watchTitle, autoGenerateSlug]);

  // Auto-generate excerpt from content
  const debouncedExcerptGeneration = useCallback(
    debounce((content: string, currentExcerpt: string) => {
      if (content && shouldUpdateExcerpt(currentExcerpt || '', content)) {
        const newExcerpt = generateExcerpt(content, 160);
        if (newExcerpt) {
          setValue('excerpt', newExcerpt, { shouldValidate: false });
        }
      }
    }, 1000),
    [setValue]
  );

  useEffect(() => {
    if (watchContent) {
      debouncedExcerptGeneration(watchContent, watchExcerpt || '');
    }
  }, [watchContent, watchExcerpt, debouncedExcerptGeneration]);

  // Auto-save functionality
  const debouncedAutoSave = useCallback(
    debounce(async () => {
      if (isDirty && watchTitle && watchContent && watchWebsiteId && !isImageUploading) {
        setAutoSaveStatus('saving');
        try {
          // Here you could implement auto-save to localStorage or backend
          localStorage.setItem('blog-draft', JSON.stringify({
            ...getValues(),
            timestamp: new Date().toISOString()
          }));
          setAutoSaveStatus('saved');
          setLastSaved(new Date());
          setTimeout(() => setAutoSaveStatus('idle'), 2000);
        } catch (error) {
          setAutoSaveStatus('error');
          setTimeout(() => setAutoSaveStatus('idle'), 3000);
        }
      }
    }, 2000),
    [isDirty, watchTitle, watchContent, watchWebsiteId, getValues, isImageUploading]
  );

  useEffect(() => {
    if (isDirty) {
      debouncedAutoSave();
    }
  }, [isDirty, debouncedAutoSave]);

  // Load draft from localStorage on mount
  useEffect(() => {
    const savedDraft = localStorage.getItem('blog-draft');
    if (savedDraft && !preselectedWebsiteId) {
      try {
        const draft = JSON.parse(savedDraft);
        const draftAge = new Date().getTime() - new Date(draft.timestamp).getTime();
        const oneHour = 60 * 60 * 1000;
        
        if (draftAge < oneHour) {
          const shouldRestore = confirm('Found a recent draft. Would you like to restore it?');
          if (shouldRestore) {
            Object.keys(draft).forEach(key => {
              if (key !== 'timestamp') {
                setValue(key as keyof BlogForm, draft[key]);
              }
            });
            if (draft.categories) setCategoriesInput(draft.categories.join(', '));
            if (draft.tags) setTagsInput(draft.tags.join(', '));
            toast.success('Draft restored successfully!');
          }
        }
      } catch (error) {
        console.error('Failed to restore draft:', error);
      }
    }
  }, [setValue, preselectedWebsiteId]);

  // Clear draft on successful submission
  const clearDraft = useCallback(() => {
    localStorage.removeItem('blog-draft');
  }, []);

  const handleImageUpload = async (file: File): Promise<string> => {
    setIsUploading(true);
    setIsImageUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'blog-content');

      const response = await mediaApi.uploadMedia(formData);
      return response.data.media.url;
    } catch (error) {
      toast.error('Failed to upload image');
      throw error;
    } finally {
      setIsUploading(false);
      setIsImageUploading(false);
    }
  };

  const handleFeaturedImageUpload = async () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        setIsImageUploading(true);
        try {
          const url = await handleImageUpload(file);
          setValue('featuredImage', url);
          toast.success('Featured image uploaded!');
        } catch (error) {
          // Error handled in handleImageUpload
        } finally {
          setIsImageUploading(false);
        }
      }
    };
    input.click();
  };

  const onSubmit = async (data: BlogForm) => {
    try {
      // Process categories and tags
      const categories = categoriesInput
        .split(',')
        .map(cat => cat.trim())
        .filter(cat => cat.length > 0);
      
      const tags = tagsInput
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      // Auto-generate excerpt if not provided
      const excerpt = data.excerpt || generateExcerpt(data.content, 160);

      const blogData = {
        ...data,
        excerpt,
        categories,
        tags,
        readingTime: calculateReadingTime(data.content),
      };

      await createBlog.mutateAsync(blogData);
      clearDraft(); // Clear the saved draft on successful submission
      toast.success(`Blog ${data.status === 'published' ? 'published' : 'saved as draft'} successfully!`);
      router.push('/blogs');
    } catch (error) {
      // Error is handled by the mutation
      console.error('Blog creation failed:', error);
    }
  };

  const handleSaveDraft = () => {
    setValue('status', 'draft');
    handleSubmit(onSubmit)();
  };

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow lowercase letters, numbers, and hyphens
    const sanitizedValue = value
      .toLowerCase()
      .replace(/[^a-z0-9-]/g, '')
      .replace(/-+/g, '-'); // Replace multiple hyphens with single hyphen

    setValue('slug', sanitizedValue, { shouldValidate: false });
    setAutoGenerateSlug(false);
  };

  const handleExcerptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= 160) {
      setValue('excerpt', value, { shouldValidate: false });
    }
  };

  const handlePublish = () => {
    setValue('status', 'published');
    handleSubmit(onSubmit)();
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-[#0F0F23]">
        {/* Top Header */}
        <header className="bg-[#1E293B] border-b border-[#334155] sticky top-0 z-50">
          <div className="flex items-center justify-between px-6 py-3">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
                className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div className="text-sm text-[#CBD5E1]">
                <span className="text-[#94A3B8]">Unpublished Changes</span>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* Auto-save Status */}
              <div className="flex items-center gap-2 text-sm">
                {autoSaveStatus === 'saving' && (
                  <>
                    <Loader2 className="h-3 w-3 animate-spin text-[#3B82F6]" />
                    <span className="text-[#3B82F6]">Saving...</span>
                  </>
                )}
                {autoSaveStatus === 'saved' && (
                  <>
                    <CheckCircle2 className="h-3 w-3 text-[#10B981]" />
                    <span className="text-[#10B981]">
                      Saved {lastSaved && new Date(lastSaved).toLocaleTimeString()}
                    </span>
                  </>
                )}
                {autoSaveStatus === 'error' && (
                  <>
                    <AlertCircle className="h-3 w-3 text-[#EF4444]" />
                    <span className="text-[#EF4444]">Save failed</span>
                  </>
                )}
              </div>

              <Button
                variant="outline"
                onClick={handleSaveDraft}
                disabled={createBlog.isPending || !watchTitle || !watchContent || !watchWebsiteId}
                className="border-[#334155] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Draft
              </Button>
              <Button
                onClick={handlePublish}
                disabled={createBlog.isPending || !watchTitle || !watchContent || !watchWebsiteId}
                className="btn-primary"
              >
                {createBlog.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Publishing...
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    Publish
                  </>
                )}
              </Button>
            </div>
          </div>
        </header>

        {/* Main Layout */}
        <div className="flex">
          {/* Main Content Area */}
          <div className="flex-1 max-w-4xl mx-auto">
            <form onSubmit={(e) => e.preventDefault()} className="p-8">
              {/* Title Input */}
              <div className="mb-8">
                <Input
                  type="text"
                  placeholder="Blog Title"
                  name="title"
                  value={watch('title') || ''}
                  onChange={(e) => setValue('title', e.target.value)}
                  className="text-4xl font-bold border-none bg-transparent p-0 h-auto text-white placeholder-[#94A3B8] focus:ring-0 focus:outline-none"
                  style={{ fontSize: '2.5rem', lineHeight: '1.2' }}
                />
                {errors.title && (
                  <p className="mt-2 text-sm text-[#EF4444] flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.title.message}
                  </p>
                )}
              </div>

              {/* Rich Text Editor */}
              <div className="mb-8">
                <Controller
                  name="content"
                  control={control}
                  render={({ field }) => (
                    <RichTextEditor
                      content={field.value || ''}
                      onChange={field.onChange}
                      onImageUpload={handleImageUpload}
                      placeholder="Start writing your blog post..."
                      className={errors.content ? 'border-[#EF4444]' : ''}
                    />
                  )}
                />
                {errors.content && (
                  <p className="mt-2 text-sm text-[#EF4444]">{errors.content.message}</p>
                )}
              </div>
            </form>
          </div>

          {/* Right Sidebar */}
          <div className="w-80 bg-[#1E293B] border-l border-[#334155] p-6 space-y-6">
            {/* Website Selection */}
            <div>
              <label className="block text-sm font-medium text-white mb-3">
                Website *
              </label>

              {websitesLoading && (
                <div className="flex items-center gap-2 p-3 border border-[#334155] rounded-lg bg-[#334155]">
                  <Loader2 className="h-4 w-4 animate-spin text-[#3B82F6]" />
                  <span className="text-sm text-[#CBD5E1]">Loading websites...</span>
                </div>
              )}

              {websitesError && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2 p-3 border border-[#EF4444] rounded-lg bg-[#EF4444]/10">
                    <AlertCircle className="h-4 w-4 text-[#EF4444]" />
                    <span className="text-sm text-[#EF4444]">Failed to load websites</span>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => refetchWebsites()}
                    className="w-full border-[#334155] text-[#CBD5E1] hover:bg-[#334155]"
                  >
                    Try Again
                  </Button>
                </div>
              )}

              {!websitesLoading && !websitesError && websites.length === 0 && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2 p-3 border border-[#F59E0B] rounded-lg bg-[#F59E0B]/10">
                    <AlertCircle className="h-4 w-4 text-[#F59E0B]" />
                    <span className="text-sm text-[#F59E0B]">No websites found</span>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => router.push('/websites/new')}
                    className="w-full border-[#334155] text-[#CBD5E1] hover:bg-[#334155]"
                  >
                    Create Your First Website
                  </Button>
                </div>
              )}

              {!websitesLoading && !websitesError && websites.length > 0 && (
                <div className="space-y-2">
                  <select
                    name="websiteId"
                    value={watch('websiteId') || ''}
                    onChange={(e) => setValue('websiteId', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg bg-[#334155] text-white focus:outline-none focus:ring-2 focus:ring-[#3B82F6] transition-colors ${
                      errors.websiteId ? 'border-[#EF4444] focus:ring-[#EF4444]' : 'border-[#475569]'
                    }`}
                  >
                    <option value="">Select a website</option>
                    {websites.map((website: any) => (
                      <option key={website._id} value={website._id}>
                        {website.name} ({website.domain})
                      </option>
                    ))}
                  </select>

                  {watchWebsiteId && (
                    <div className="flex items-center gap-2 p-2 bg-[#10B981]/10 border border-[#10B981] rounded-lg">
                      <CheckCircle2 className="h-4 w-4 text-[#10B981]" />
                      <span className="text-sm text-[#10B981]">
                        Website selected: {websites.find((w: any) => w._id === watchWebsiteId)?.name}
                      </span>
                    </div>
                  )}
                </div>
              )}

              {errors.websiteId && (
                <p className="mt-1 text-sm text-[#EF4444] flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.websiteId.message}
                </p>
              )}
            </div>

            {/* Publish Status */}
            <div>
              <label className="block text-sm font-medium text-white mb-3">
                Status
              </label>
              <select
                name="status"
                value={watch('status') || 'draft'}
                onChange={(e) => setValue('status', e.target.value as 'draft' | 'published' | 'scheduled')}
                className="w-full px-3 py-2 border border-[#475569] bg-[#334155] text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3B82F6]"
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="scheduled">Scheduled</option>
              </select>
            </div>

            {watch('status') === 'scheduled' && (
              <div>
                <label className="block text-sm font-medium text-white mb-3">
                  Schedule Date & Time *
                </label>
                <input
                  type="datetime-local"
                  name="scheduledAt"
                  value={watch('scheduledAt') || ''}
                  onChange={(e) => setValue('scheduledAt', e.target.value)}
                  className="w-full px-3 py-2 border border-[#475569] bg-[#334155] text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3B82F6]"
                  min={new Date().toISOString().slice(0, 16)}
                />
                {errors.scheduledAt && (
                  <p className="mt-1 text-sm text-[#EF4444]">{errors.scheduledAt.message}</p>
                )}
              </div>
            )}

            {/* Blog Settings */}
            <div>
              <h3 className="text-sm font-medium text-white mb-3">Blog Settings</h3>
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="isFeatured"
                    checked={watch('isFeatured') || false}
                    onChange={(e) => setValue('isFeatured', e.target.checked)}
                    className="mr-3 rounded border-[#475569] text-[#3B82F6] focus:ring-[#3B82F6] bg-[#334155]"
                  />
                  <span className="text-sm text-[#CBD5E1]">Featured post</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="allowComments"
                    checked={watch('allowComments') || false}
                    onChange={(e) => setValue('allowComments', e.target.checked)}
                    className="mr-3 rounded border-[#475569] text-[#3B82F6] focus:ring-[#3B82F6] bg-[#334155]"
                  />
                  <span className="text-sm text-[#CBD5E1]">Allow comments</span>
                </label>
              </div>
            </div>

            {/* URL Slug */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-white">
                  URL Slug *
                </label>
                <button
                  type="button"
                  onClick={() => setAutoGenerateSlug(!autoGenerateSlug)}
                  className="text-xs text-[#3B82F6] hover:text-[#60A5FA]"
                >
                  {autoGenerateSlug ? 'Manual edit' : 'Auto-generate'}
                </button>
              </div>
              <Input
                type="text"
                placeholder="blog-post-url"
                value={watchSlug || ''}
                onChange={handleSlugChange}
                className={`input-dark ${errors.slug ? 'border-[#EF4444] focus:ring-[#EF4444]' : ''} ${autoGenerateSlug ? 'bg-[#334155]/50' : ''}`}
                readOnly={autoGenerateSlug}
              />
              {errors.slug && (
                <p className="mt-1 text-sm text-[#EF4444] flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.slug.message}
                </p>
              )}
              {watchSlug && (
                <p className="mt-1 text-xs text-[#94A3B8]">
                  URL: /blog/{watchSlug}
                </p>
              )}
            </div>

            {/* Excerpt */}
            <div>
              <label className="block text-sm font-medium text-white mb-3">
                Excerpt
              </label>
              <Textarea
                rows={4}
                placeholder="Brief description of your blog post (auto-generated from content)"
                value={watch('excerpt') || ''}
                onChange={handleExcerptChange}
                className={`bg-[#334155] border-[#475569] text-white placeholder-[#94A3B8] focus:ring-[#3B82F6] focus:border-[#3B82F6] ${errors.excerpt ? 'border-[#EF4444] focus:ring-[#EF4444]' : ''}`}
                maxLength={160}
              />
              {errors.excerpt && (
                <p className="mt-1 text-sm text-[#EF4444] flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.excerpt.message}
                </p>
              )}
              <p className={`mt-1 text-xs ${
                (watch('excerpt')?.length || 0) >= 160
                  ? 'text-[#EF4444]'
                  : (watch('excerpt')?.length || 0) >= 140
                    ? 'text-[#F59E0B]'
                    : 'text-[#94A3B8]'
              }`}>
                {watch('excerpt')?.length || 0}/160 characters
                {(watch('excerpt')?.length || 0) >= 160 && ' (limit reached)'}
              </p>
            </div>

            {/* Featured Image */}
            <div>
              <label className="block text-sm font-medium text-white mb-3">
                Featured Image
              </label>
              <div className="space-y-3">
                {watch('featuredImage') ? (
                  <div className="space-y-2">
                    <img
                      src={watch('featuredImage')}
                      alt="Featured"
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    <div className="flex space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setIsMediaPickerOpen(true)}
                        className="border-[#334155] text-[#CBD5E1] hover:bg-[#334155]"
                      >
                        Change Image
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setValue('featuredImage', '')}
                        className="border-[#334155] text-[#CBD5E1] hover:bg-[#334155]"
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      className="flex-1 border-[#334155] text-[#CBD5E1] hover:bg-[#334155]"
                      onClick={() => setIsMediaPickerOpen(true)}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Select from Media
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleFeaturedImageUpload}
                      disabled={isImageUploading}
                      className="border-[#334155] text-[#CBD5E1] hover:bg-[#334155]"
                    >
                      {isImageUploading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Upload className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Categories & Tags */}
            <div>
              <label className="block text-sm font-medium text-white mb-3">
                Categories
              </label>
              <Input
                type="text"
                placeholder="Technology, Web Development"
                value={categoriesInput}
                onChange={(e) => setCategoriesInput(e.target.value)}
                className="input-dark"
              />
              <p className="text-xs text-[#94A3B8] mt-1">Separate with commas</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-3">
                Tags
              </label>
              <Input
                type="text"
                placeholder="react, nextjs, javascript"
                value={tagsInput}
                onChange={(e) => setTagsInput(e.target.value)}
                className="input-dark"
              />
              <p className="text-xs text-[#94A3B8] mt-1">Separate with commas</p>
            </div>
          </div>
        </div>

        {/* Media Picker */}
        <MediaPicker
          isOpen={isMediaPickerOpen}
          onClose={() => setIsMediaPickerOpen(false)}
          onSelect={(url) => setValue('featuredImage', url)}
          selectedUrl={watch('featuredImage')}
          allowUpload={true}
          acceptedTypes={['image/*']}
        />
      </div>
    </DashboardLayout>
  );
}
