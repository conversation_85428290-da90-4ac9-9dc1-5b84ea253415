'use client';

import { useState } from 'react';
import Link from 'next/link';
import { 
  ArrowLeft,
  Search, 
  Filter,
  RotateCcw,
  Trash2,
  Calendar,
  User,
  Globe,
  MoreHorizontal,
  RefreshCw
} from 'lucide-react';
import { useTrashedBlogs, useRestoreBlog, usePermanentDeleteBlog } from '@/hooks/use-blogs';
import { useMyWebsites } from '@/hooks/use-dashboard';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate, formatRelativeTime } from '@/lib/utils';
import toast from 'react-hot-toast';

interface TrashedBlogCardProps {
  blog: any;
}

function TrashedBlogCard({ blog }: TrashedBlogCardProps) {
  const restoreBlog = useRestoreBlog();
  const permanentDeleteBlog = usePermanentDeleteBlog();

  const handleRestore = async () => {
    if (window.confirm('Are you sure you want to restore this blog?')) {
      restoreBlog.mutate(blog._id);
    }
  };

  const handlePermanentDelete = async () => {
    if (window.confirm('Are you sure you want to permanently delete this blog? This action cannot be undone.')) {
      permanentDeleteBlog.mutate(blog._id);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'success';
      case 'draft':
        return 'secondary';
      case 'scheduled':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  return (
    <Card className="card-dark hover:border-[#475569] transition-all duration-200">
      <CardHeader className="border-b border-[#334155]">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-white text-lg mb-2 line-clamp-2">
              {blog.title}
            </CardTitle>
            <CardDescription className="text-[#94A3B8] line-clamp-2">
              {blog.excerpt}
            </CardDescription>
          </div>
          <Badge variant={getStatusColor(blog.status) as any} className="ml-4 flex-shrink-0">
            {blog.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Metadata */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center text-[#94A3B8]">
              <User className="h-4 w-4 mr-2" />
              <span>{blog.authorId?.firstName} {blog.authorId?.lastName}</span>
            </div>
            <div className="flex items-center text-[#94A3B8]">
              <Globe className="h-4 w-4 mr-2" />
              <span>{blog.websiteId?.name}</span>
            </div>
            <div className="flex items-center text-[#94A3B8]">
              <Calendar className="h-4 w-4 mr-2" />
              <span>Deleted {formatRelativeTime(blog.deletedAt)}</span>
            </div>
            <div className="flex items-center text-[#94A3B8]">
              <Calendar className="h-4 w-4 mr-2" />
              <span>Created {formatDate(blog.createdAt)}</span>
            </div>
          </div>

          {/* Categories and Tags */}
          {(blog.categories?.length > 0 || blog.tags?.length > 0) && (
            <div className="space-y-2">
              {blog.categories?.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {blog.categories.slice(0, 3).map((category: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {category}
                    </Badge>
                  ))}
                  {blog.categories.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{blog.categories.length - 3} more
                    </Badge>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between pt-4 border-t border-[#334155]">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRestore}
                disabled={restoreBlog.isPending}
                className="border-[#475569] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Restore
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handlePermanentDelete}
                disabled={permanentDeleteBlog.isPending}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete Forever
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function TrashedBlogCardSkeleton() {
  return (
    <Card className="card-dark">
      <CardHeader className="border-b border-[#334155]">
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-2">
            <Skeleton className="h-6 w-3/4 bg-[#334155]" />
            <Skeleton className="h-4 w-full bg-[#334155]" />
          </div>
          <Skeleton className="h-6 w-16 bg-[#334155]" />
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-4 w-full bg-[#334155]" />
            ))}
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-6 w-16 bg-[#334155]" />
            <Skeleton className="h-6 w-16 bg-[#334155]" />
          </div>
          <div className="flex justify-between pt-4">
            <div className="flex space-x-2">
              <Skeleton className="h-8 w-20 bg-[#334155]" />
              <Skeleton className="h-8 w-24 bg-[#334155]" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function TrashPage() {
  const [search, setSearch] = useState('');
  const [websiteFilter, setWebsiteFilter] = useState('');

  const { data: trashedBlogs, isLoading, refetch } = useTrashedBlogs({ 
    search, 
    websiteId: websiteFilter 
  });
  const { data: websites } = useMyWebsites();

  const handleRefresh = () => {
    refetch();
    toast.success('Refreshed trash');
  };

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/blogs">
              <Button
                variant="ghost"
                size="icon"
                className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Trash</h1>
              <p className="text-[#CBD5E1]">Restore or permanently delete trashed blog posts</p>
            </div>
          </div>
          <Button
            variant="outline"
            onClick={handleRefresh}
            className="border-[#475569] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#94A3B8] h-4 w-4" />
            <Input
              placeholder="Search trashed blogs..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10"
            />
          </div>
          <select
            value={websiteFilter}
            onChange={(e) => setWebsiteFilter(e.target.value)}
            className="px-3 py-2 bg-[#1E293B] border border-[#475569] rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:border-[#3B82F6]"
          >
            <option value="">All Websites</option>
            {websites?.data?.map((website: any) => (
              <option key={website._id} value={website._id}>
                {website.name}
              </option>
            ))}
          </select>
        </div>

        {/* Content */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <TrashedBlogCardSkeleton key={i} />
            ))}
          </div>
        ) : trashedBlogs?.data?.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gradient-to-br from-[#3B82F6] to-[#1E40AF] rounded-full flex items-center justify-center mx-auto mb-4">
              <Trash2 className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Trash is empty</h3>
            <p className="text-[#94A3B8] mb-6">No deleted blog posts found</p>
            <Link href="/blogs">
              <Button className="btn-primary">
                Back to Blogs
              </Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {trashedBlogs?.data?.map((blog: any) => (
              <TrashedBlogCard key={blog._id} blog={blog} />
            ))}
          </div>
        )}

        {/* Pagination */}
        {trashedBlogs?.data?.totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex items-center space-x-2">
              {/* Add pagination controls here if needed */}
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
