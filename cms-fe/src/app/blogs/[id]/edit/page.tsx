'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Loader2, Save, Eye, Calendar, Upload, Trash2 } from 'lucide-react';
import { useBlog, useUpdateBlog, useDeleteBlog, usePublishBlog } from '@/hooks/use-blogs';
import { useMyWebsites } from '@/hooks/use-dashboard';
import { mediaApi } from '@/lib/api';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { RichTextEditor } from '@/components/editor/rich-text-editor';
import { MediaPicker } from '@/components/media/media-picker';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { generateSlug, calculateReadingTime } from '@/lib/utils';
import toast from 'react-hot-toast';

const blogSchema = z.object({
  title: z.string().min(2, 'Title must be at least 2 characters'),
  slug: z.string()
    .min(2, 'Slug must be at least 2 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
    .refine((slug) => !slug.startsWith('-') && !slug.endsWith('-'), 'Slug cannot start or end with a hyphen'),
  content: z.string().min(10, 'Content must be at least 10 characters'),
  excerpt: z.string().min(10, 'Excerpt must be at least 10 characters'),
  websiteId: z.string().min(1, 'Please select a website'),
  featuredImage: z.string().url().optional().or(z.literal('')),
  status: z.enum(['draft', 'published', 'scheduled']).default('draft'),
  categories: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  scheduledAt: z.string().optional(),
  isFeatured: z.boolean().default(false),
  allowComments: z.boolean().default(true),
  seo: z.object({
    seoTitle: z.string().optional(),
    seoDescription: z.string().optional(),
    focusKeyword: z.string().optional(),
    canonicalUrl: z.string().url().optional().or(z.literal('')),
    ogTitle: z.string().optional(),
    ogDescription: z.string().optional(),
    ogImage: z.string().url().optional().or(z.literal('')),
    noIndex: z.boolean().default(false),
    noFollow: z.boolean().default(false),
  }).optional(),
});

type BlogForm = z.infer<typeof blogSchema>;

interface EditBlogPageProps {
  params: {
    id: string;
  };
}

export default function EditBlogPage({ params }: EditBlogPageProps) {
  const router = useRouter();
  const { data: blog, isLoading: blogLoading } = useBlog(params.id);
  const { data: websites } = useMyWebsites();
  
  const updateBlog = useUpdateBlog();
  const deleteBlog = useDeleteBlog();
  const publishBlog = usePublishBlog();
  
  const [categoriesInput, setCategoriesInput] = useState('');
  const [tagsInput, setTagsInput] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [isMediaPickerOpen, setIsMediaPickerOpen] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    reset,
    formState: { errors, isDirty },
  } = useForm<BlogForm>({
    resolver: zodResolver(blogSchema),
  });

  const watchTitle = watch('title');
  const watchContent = watch('content');

  // Initialize form with blog data
  useEffect(() => {
    if (blog) {
      reset({
        title: blog.title,
        slug: blog.slug,
        content: blog.content,
        excerpt: blog.excerpt,
        websiteId: blog.websiteId._id || blog.websiteId,
        featuredImage: blog.featuredImage || '',
        status: blog.status,
        categories: blog.categories || [],
        tags: blog.tags || [],
        scheduledAt: blog.scheduledAt || '',
        isFeatured: blog.isFeatured || false,
        allowComments: blog.allowComments !== false,
        seo: {
          seoTitle: blog.seo?.seoTitle || '',
          seoDescription: blog.seo?.seoDescription || '',
          focusKeyword: blog.seo?.focusKeyword || '',
          canonicalUrl: blog.seo?.canonicalUrl || '',
          ogTitle: blog.seo?.ogTitle || '',
          ogDescription: blog.seo?.ogDescription || '',
          ogImage: blog.seo?.ogImage || '',
          noIndex: blog.seo?.noIndex || false,
          noFollow: blog.seo?.noFollow || false,
        },
      });
      
      setCategoriesInput(blog.categories?.join(', ') || '');
      setTagsInput(blog.tags?.join(', ') || '');
    }
  }, [blog, reset]);

  // Auto-generate slug from title
  useEffect(() => {
    if (watchTitle && blog && watchTitle !== blog.title) {
      const slug = generateSlug(watchTitle);
      setValue('slug', slug);
    }
  }, [watchTitle, setValue, blog]);

  const handleImageUpload = async (file: File): Promise<string> => {
    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'blog-content');
      
      const response = await mediaApi.uploadMedia(formData);
      return response.data.media.url;
    } catch (error) {
      toast.error('Failed to upload image');
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  const handleFeaturedImageUpload = async () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          const url = await handleImageUpload(file);
          setValue('featuredImage', url);
          toast.success('Featured image uploaded!');
        } catch (error) {
          // Error handled in handleImageUpload
        }
      }
    };
    input.click();
  };

  const onSubmit = async (data: BlogForm) => {
    try {
      // Process categories and tags
      const categories = categoriesInput
        .split(',')
        .map(cat => cat.trim())
        .filter(cat => cat.length > 0);
      
      const tags = tagsInput
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      const blogData = {
        ...data,
        categories,
        tags,
        readingTime: calculateReadingTime(data.content),
      };

      const updatedBlog = await updateBlog.mutateAsync({ id: params.id, data: blogData });
      // Stay on the edit page to show the updated content
      // router.push('/blogs');
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleSaveDraft = () => {
    setValue('status', 'draft');
    handleSubmit(onSubmit)();
  };

  const handleExcerptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= 160) {
      setValue('excerpt', value, { shouldValidate: false });
    }
  };

  const handlePublish = async () => {
    if (blog?.status === 'draft') {
      if (window.confirm('Are you sure you want to publish this blog?')) {
        publishBlog.mutate(params.id);
      }
    } else {
      setValue('status', 'published');
      handleSubmit(onSubmit)();
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this blog? This action cannot be undone.')) {
      try {
        await deleteBlog.mutateAsync(params.id);
        router.push('/blogs');
      } catch (error) {
        // Error is handled by the mutation
      }
    }
  };

  if (blogLoading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center space-x-4 mb-8">
              <Skeleton className="h-10 w-10" />
              <div className="space-y-2">
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <Skeleton className="h-6 w-32" />
                </CardHeader>
                <CardContent className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-64 w-full" />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!blog) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Blog not found</h3>
            <p className="text-gray-600 mb-4">
              The blog you're looking for doesn't exist or you don't have access to it.
            </p>
            <Button onClick={() => router.push('/blogs')}>
              Back to Blogs
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Edit Blog Post</h1>
                <div className="flex items-center space-x-2">
                  <p className="text-gray-600">
                    {blog.title}
                  </p>
                  <Badge variant={blog.status === 'published' ? 'success' : 'secondary'}>
                    {blog.status}
                  </Badge>
                </div>
                <div className="flex items-center space-x-4 text-sm text-gray-500 mt-2">
                  <span>Author: {blog.authorId?.firstName} {blog.authorId?.lastName}</span>
                  <span>•</span>
                  <span>Created: {new Date(blog.createdAt).toLocaleDateString()}</span>
                  <span>•</span>
                  <span>Updated: {new Date(blog.updatedAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={deleteBlog.isPending}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
              {blog?.status === 'published' && (
                <Button
                  variant="outline"
                  onClick={() => router.push(`/blogs/${params.id}`)}
                  className="border-[#475569] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Blog
                </Button>
              )}
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                disabled={updateBlog.isPending || !isDirty}
              >
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
              <Button
                onClick={handlePublish}
                disabled={updateBlog.isPending || publishBlog.isPending}
              >
                {updateBlog.isPending || publishBlog.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {blog.status === 'draft' ? 'Publishing...' : 'Updating...'}
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    {blog.status === 'draft' ? 'Publish' : 'Update & Publish'}
                  </>
                )}
              </Button>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* Basic Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>Blog Content</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                        Title *
                      </label>
                      <Input
                        id="title"
                        placeholder="Enter your blog title"
                        {...register('title')}
                        className={errors.title ? 'border-red-500' : ''}
                      />
                      {errors.title && (
                        <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-1">
                        Slug *
                      </label>
                      <Input
                        id="slug"
                        placeholder="blog-post-url"
                        {...register('slug')}
                        className={errors.slug ? 'border-red-500' : ''}
                      />
                      {errors.slug && (
                        <p className="mt-1 text-sm text-red-600">{errors.slug.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Content *
                      </label>
                      <Controller
                        name="content"
                        control={control}
                        render={({ field }) => (
                          <RichTextEditor
                            content={field.value || ''}
                            onChange={field.onChange}
                            onImageUpload={handleImageUpload}
                            className={errors.content ? 'border-red-500' : ''}
                          />
                        )}
                      />
                      {errors.content && (
                        <p className="mt-1 text-sm text-red-600">{errors.content.message}</p>
                      )}
                      {isUploading && (
                        <p className="mt-1 text-sm text-blue-600">Uploading image...</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="excerpt" className="block text-sm font-medium text-white mb-2">
                        Excerpt *
                      </label>
                      <Textarea
                        id="excerpt"
                        rows={4}
                        placeholder="Brief description of your blog post"
                        value={watch('excerpt') || ''}
                        onChange={handleExcerptChange}
                        className={errors.excerpt ? 'border-red-500 focus:border-red-500' : ''}
                        maxLength={160}
                      />
                      {errors.excerpt && (
                        <p className="mt-1 text-sm text-red-400">{errors.excerpt.message}</p>
                      )}
                      <p className={`mt-1 text-xs ${
                        (watch('excerpt')?.length || 0) >= 160
                          ? 'text-red-400'
                          : (watch('excerpt')?.length || 0) >= 140
                            ? 'text-yellow-400'
                            : 'text-[#94A3B8]'
                      }`}>
                        {watch('excerpt')?.length || 0}/160 characters
                        {(watch('excerpt')?.length || 0) >= 160 && ' (limit reached)'}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Publish Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle>Publish Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label htmlFor="websiteId" className="block text-sm font-medium text-gray-700 mb-1">
                        Website *
                      </label>
                      <select
                        id="websiteId"
                        {...register('websiteId')}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors.websiteId ? 'border-red-500' : 'border-gray-300'
                        }`}
                      >
                        <option value="">Select a website</option>
                        {websites?.websites?.map((website: any) => (
                          <option key={website._id} value={website._id}>
                            {website.name}
                          </option>
                        ))}
                      </select>
                      {errors.websiteId && (
                        <p className="mt-1 text-sm text-red-600">{errors.websiteId.message}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                        Status
                      </label>
                      <select
                        id="status"
                        {...register('status')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="draft">Draft</option>
                        <option value="published">Published</option>
                        <option value="scheduled">Scheduled</option>
                      </select>
                    </div>

                    {watch('status') === 'scheduled' && (
                      <div>
                        <label htmlFor="scheduledAt" className="block text-sm font-medium text-gray-700 mb-1">
                          Schedule Date & Time *
                        </label>
                        <input
                          id="scheduledAt"
                          type="datetime-local"
                          {...register('scheduledAt')}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min={new Date().toISOString().slice(0, 16)}
                        />
                        {errors.scheduledAt && (
                          <p className="mt-1 text-sm text-red-600">{errors.scheduledAt.message}</p>
                        )}
                      </div>
                    )}

                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register('isFeatured')}
                          className="mr-2"
                        />
                        <span className="text-sm">Featured post</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register('allowComments')}
                          className="mr-2"
                        />
                        <span className="text-sm">Allow comments</span>
                      </label>
                    </div>
                  </CardContent>
                </Card>

                {/* Featured Image */}
                <Card>
                  <CardHeader>
                    <CardTitle>Featured Image</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {watch('featuredImage') ? (
                        <div className="space-y-2">
                          <img
                            src={watch('featuredImage')}
                            alt="Featured"
                            className="w-full h-32 object-cover rounded"
                          />
                          <div className="flex space-x-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => setIsMediaPickerOpen(true)}
                            >
                              Change Image
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => setValue('featuredImage', '')}
                            >
                              Remove
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            className="flex-1"
                            onClick={() => setIsMediaPickerOpen(true)}
                          >
                            <Upload className="h-4 w-4 mr-2" />
                            Select from Media
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={handleFeaturedImageUpload}
                            disabled={isUploading}
                          >
                            {isUploading ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Upload className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Categories & Tags */}
                <Card>
                  <CardHeader>
                    <CardTitle>Categories & Tags</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label htmlFor="categories" className="block text-sm font-medium text-gray-700 mb-1">
                        Categories
                      </label>
                      <Input
                        id="categories"
                        placeholder="Technology, Web Development"
                        value={categoriesInput}
                        onChange={(e) => setCategoriesInput(e.target.value)}
                      />
                      <p className="text-xs text-gray-500 mt-1">Separate with commas</p>
                    </div>

                    <div>
                      <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-1">
                        Tags
                      </label>
                      <Input
                        id="tags"
                        placeholder="react, nextjs, javascript"
                        value={tagsInput}
                        onChange={(e) => setTagsInput(e.target.value)}
                      />
                      <p className="text-xs text-gray-500 mt-1">Separate with commas</p>
                    </div>
                  </CardContent>
                </Card>

                {/* SEO Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle>SEO Settings</CardTitle>
                    <CardDescription>
                      Optimize your blog post for search engines
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label htmlFor="seoTitle" className="block text-sm font-medium text-gray-700 mb-1">
                        SEO Title
                      </label>
                      <Input
                        id="seoTitle"
                        placeholder="Custom title for search engines"
                        {...register('seo.seoTitle')}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        {watch('seo.seoTitle')?.length || 0}/60 characters (recommended)
                      </p>
                    </div>

                    <div>
                      <label htmlFor="seoDescription" className="block text-sm font-medium text-gray-700 mb-1">
                        SEO Description
                      </label>
                      <textarea
                        id="seoDescription"
                        rows={3}
                        placeholder="Brief description for search results"
                        {...register('seo.seoDescription')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        {watch('seo.seoDescription')?.length || 0}/160 characters (recommended)
                      </p>
                    </div>

                    <div>
                      <label htmlFor="focusKeyword" className="block text-sm font-medium text-gray-700 mb-1">
                        Focus Keyword
                      </label>
                      <Input
                        id="focusKeyword"
                        placeholder="Main keyword for this post"
                        {...register('seo.focusKeyword')}
                      />
                    </div>

                    <div>
                      <label htmlFor="canonicalUrl" className="block text-sm font-medium text-gray-700 mb-1">
                        Canonical URL
                      </label>
                      <Input
                        id="canonicalUrl"
                        placeholder="https://example.com/canonical-url"
                        {...register('seo.canonicalUrl')}
                      />
                    </div>

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-gray-700">Open Graph</h4>

                      <div>
                        <label htmlFor="ogTitle" className="block text-sm font-medium text-gray-600 mb-1">
                          OG Title
                        </label>
                        <Input
                          id="ogTitle"
                          placeholder="Title for social media sharing"
                          {...register('seo.ogTitle')}
                        />
                      </div>

                      <div>
                        <label htmlFor="ogDescription" className="block text-sm font-medium text-gray-600 mb-1">
                          OG Description
                        </label>
                        <textarea
                          id="ogDescription"
                          rows={2}
                          placeholder="Description for social media sharing"
                          {...register('seo.ogDescription')}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div>
                        <label htmlFor="ogImage" className="block text-sm font-medium text-gray-600 mb-1">
                          OG Image URL
                        </label>
                        <Input
                          id="ogImage"
                          placeholder="https://example.com/image.jpg"
                          {...register('seo.ogImage')}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-gray-700">Robot Settings</h4>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register('seo.noIndex')}
                          className="mr-2"
                        />
                        <span className="text-sm">No Index (prevent search engine indexing)</span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register('seo.noFollow')}
                          className="mr-2"
                        />
                        <span className="text-sm">No Follow (prevent following links)</span>
                      </label>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Media Picker */}
      <MediaPicker
        isOpen={isMediaPickerOpen}
        onClose={() => setIsMediaPickerOpen(false)}
        onSelect={(url) => setValue('featuredImage', url)}
        selectedUrl={watch('featuredImage')}
        allowUpload={true}
        acceptedTypes={['image/*']}
      />
    </DashboardLayout>
  );
}
