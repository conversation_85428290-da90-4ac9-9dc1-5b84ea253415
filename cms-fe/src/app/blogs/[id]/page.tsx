'use client';

import { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Calendar, User, Globe, Eye, Share2, Edit, Trash2 } from 'lucide-react';
import { useBlog, useDeleteBlog } from '@/hooks/use-blogs';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate, formatRelativeTime } from '@/lib/utils';

function BlogDetailSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Skeleton className="h-10 w-10 bg-[#334155]" />
        <Skeleton className="h-8 w-48 bg-[#334155]" />
      </div>
      
      <Card className="card-dark">
        <CardHeader>
          <Skeleton className="h-8 w-3/4 bg-[#334155]" />
          <div className="flex items-center space-x-4 mt-4">
            <Skeleton className="h-6 w-20 bg-[#334155]" />
            <Skeleton className="h-6 w-32 bg-[#334155]" />
            <Skeleton className="h-6 w-24 bg-[#334155]" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-4 w-full bg-[#334155]" />
            <Skeleton className="h-4 w-full bg-[#334155]" />
            <Skeleton className="h-4 w-3/4 bg-[#334155]" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function BlogDetailPage() {
  const params = useParams();
  const router = useRouter();
  const blogId = params.id as string;
  
  const { data: blog, isLoading, error } = useBlog(blogId);
  const deleteBlog = useDeleteBlog();

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this blog? This action cannot be undone.')) {
      try {
        await deleteBlog.mutateAsync(blogId);
        router.push('/blogs');
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'success';
      case 'draft':
        return 'secondary';
      case 'scheduled':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <BlogDetailSkeleton />
        </div>
      </DashboardLayout>
    );
  }

  if (error || !blog) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gradient-to-br from-[#EF4444] to-[#DC2626] rounded-full flex items-center justify-center mx-auto mb-4">
              <Trash2 className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Blog not found</h3>
            <p className="text-[#94A3B8] mb-6">The blog post you're looking for doesn't exist or has been deleted.</p>
            <Link href="/blogs">
              <Button className="btn-primary">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Blogs
              </Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/blogs">
              <Button
                variant="ghost"
                size="icon"
                className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Blog Details</h1>
              <p className="text-[#CBD5E1]">View and manage your blog post</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Link href={`/blogs/${blogId}/edit`}>
              <Button
                variant="outline"
                className="border-[#475569] text-[#CBD5E1] hover:bg-[#334155] hover:text-white"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </Link>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteBlog.isPending}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        {/* Blog Content */}
        <div className="space-y-6">
          {/* Main Content Card */}
          <Card className="card-dark">
            <CardHeader className="border-b border-[#334155]">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-3">
                    <Badge variant={getStatusColor(blog.status) as any}>
                      {blog.status}
                    </Badge>
                    {blog.isFeatured && (
                      <Badge variant="outline" className="border-[#F59E0B] text-[#F59E0B]">
                        ⭐ Featured
                      </Badge>
                    )}
                  </div>
                  <CardTitle className="text-2xl text-white mb-4">
                    {blog.title}
                  </CardTitle>
                  {blog.excerpt && (
                    <p className="text-[#94A3B8] text-lg leading-relaxed">
                      {blog.excerpt}
                    </p>
                  )}
                </div>
                {blog.featuredImage && (
                  <img
                    src={blog.featuredImage}
                    alt={blog.title}
                    className="w-32 h-32 object-cover rounded-lg ml-6 border border-[#334155]"
                  />
                )}
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                {/* Metadata */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center text-[#94A3B8]">
                    <User className="h-4 w-4 mr-2" />
                    <span>{blog.authorId?.firstName} {blog.authorId?.lastName}</span>
                  </div>
                  <div className="flex items-center text-[#94A3B8]">
                    <Globe className="h-4 w-4 mr-2" />
                    <span>{blog.websiteId?.name}</span>
                  </div>
                  <div className="flex items-center text-[#94A3B8]">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>{formatDate(blog.createdAt)}</span>
                  </div>
                  <div className="flex items-center text-[#94A3B8]">
                    <Eye className="h-4 w-4 mr-2" />
                    <span>{blog.viewCount || 0} views</span>
                  </div>
                </div>

                {/* Categories and Tags */}
                {(blog.categories?.length > 0 || blog.tags?.length > 0) && (
                  <div className="space-y-3">
                    {blog.categories?.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-white mb-2">Categories</h4>
                        <div className="flex flex-wrap gap-2">
                          {blog.categories.map((category: string, index: number) => (
                            <Badge key={index} variant="outline" className="border-[#334155] text-[#CBD5E1]">
                              {category}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {blog.tags?.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-white mb-2">Tags</h4>
                        <div className="flex flex-wrap gap-2">
                          {blog.tags.map((tag: string, index: number) => (
                            <Badge key={index} variant="secondary" className="bg-[#334155] text-[#CBD5E1]">
                              #{tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Content */}
                <div>
                  <h4 className="text-lg font-medium text-white mb-4">Content</h4>
                  <div 
                    className="prose prose-invert max-w-none"
                    dangerouslySetInnerHTML={{ __html: blog.content }}
                  />
                </div>

                {/* SEO Information */}
                {blog.seo && (
                  <div className="border-t border-[#334155] pt-6">
                    <h4 className="text-lg font-medium text-white mb-4">SEO Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      {blog.seo.seoTitle && (
                        <div>
                          <span className="text-[#94A3B8]">SEO Title:</span>
                          <p className="text-white mt-1">{blog.seo.seoTitle}</p>
                        </div>
                      )}
                      {blog.seo.seoDescription && (
                        <div>
                          <span className="text-[#94A3B8]">SEO Description:</span>
                          <p className="text-white mt-1">{blog.seo.seoDescription}</p>
                        </div>
                      )}
                      {blog.seo.focusKeyword && (
                        <div>
                          <span className="text-[#94A3B8]">Focus Keyword:</span>
                          <p className="text-white mt-1">{blog.seo.focusKeyword}</p>
                        </div>
                      )}
                      {blog.seo.canonicalUrl && (
                        <div>
                          <span className="text-[#94A3B8]">Canonical URL:</span>
                          <p className="text-white mt-1">{blog.seo.canonicalUrl}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-6 border-t border-[#334155]">
                  <div className="flex items-center space-x-4 text-sm text-[#94A3B8]">
                    <span>Last updated: {formatRelativeTime(blog.updatedAt)}</span>
                    {blog.publishedAt && (
                      <span>Published: {formatRelativeTime(blog.publishedAt)}</span>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
                    >
                      <Share2 className="h-4 w-4 mr-1" />
                      Share
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-[#CBD5E1] hover:text-white hover:bg-[#334155]"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Preview
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
