// User types
export interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'editor' | 'viewer';
  isActive: boolean;
  avatar?: string;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  fullName?: string;
}

// Website types
export interface Website {
  _id: string;
  name: string;
  domain: string;
  apiKey: string;
  ownerId: string | User;
  seoSettings: {
    defaultTitle?: string;
    defaultDescription?: string;
    favicon?: string;
    ogImage?: string;
    googleAnalyticsId?: string;
    googleSearchConsoleId?: string;
  };
  theme: 'minimal' | 'magazine' | 'corporate';
  isActive: boolean;
  blogCount: number;
  description?: string;
  logo?: string;
  allowedDomains: string[];
  customSettings: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Blog types
export interface Blog {
  _id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featuredImage?: string;
  websiteId: string | Website;
  authorId: string | User;
  status: 'draft' | 'published' | 'scheduled' | 'archived';
  seo: {
    seoTitle?: string;
    seoDescription?: string;
    focusKeyword?: string;
    canonicalUrl?: string;
    ogTitle?: string;
    ogDescription?: string;
    ogImage?: string;
    noIndex: boolean;
    noFollow: boolean;
    seoScore?: number;
  };
  categories: string[];
  tags: string[];
  publishedAt?: string;
  scheduledAt?: string;
  viewCount: number;
  shareCount: number;
  readingTime: number;
  isFeatured: boolean;
  allowComments: boolean;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Media types
export interface Media {
  _id: string;
  filename: string;
  originalName: string;
  url: string;
  publicId: string;
  type: 'image' | 'video' | 'document' | 'audio';
  mimeType: string;
  size: number;
  width?: number;
  height?: number;
  uploadedBy: string | User;
  alt?: string;
  caption?: string;
  tags: string[];
  folder?: string;
  metadata: Record<string, any>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// API Response types
export interface ApiResponse<T = any> {
  data?: T;
  message: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  message: string;
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: 'admin' | 'editor' | 'viewer';
}

export interface CreateUserForm {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'editor' | 'viewer';
}

export interface CreateWebsiteForm {
  name: string;
  domain: string;
  description?: string;
  theme?: 'minimal' | 'magazine' | 'corporate';
  seoSettings?: {
    defaultTitle?: string;
    defaultDescription?: string;
    favicon?: string;
    ogImage?: string;
    googleAnalyticsId?: string;
    googleSearchConsoleId?: string;
  };
}

export interface CreateBlogForm {
  title: string;
  slug?: string;
  content: string;
  excerpt: string;
  featuredImage?: string;
  websiteId: string;
  status?: 'draft' | 'published' | 'scheduled';
  categories?: string[];
  tags?: string[];
  scheduledAt?: string;
  isFeatured?: boolean;
  allowComments?: boolean;
  seo?: {
    seoTitle?: string;
    seoDescription?: string;
    focusKeyword?: string;
    canonicalUrl?: string;
    ogTitle?: string;
    ogDescription?: string;
    ogImage?: string;
    noIndex?: boolean;
    noFollow?: boolean;
  };
}

// Query types
export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Dashboard stats
export interface DashboardStats {
  websites: {
    total: number;
    active: number;
    inactive: number;
  };
  blogs: {
    total: number;
    published: number;
    draft: number;
    scheduled: number;
    totalViews: number;
  };
  media: {
    total: number;
    totalSize: number;
    totalSizeMB: number;
  };
  users?: {
    total: number;
    active: number;
    inactive: number;
  };
}

// Navigation types
export interface NavItem {
  title: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  badge?: string | number;
  children?: NavItem[];
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';
