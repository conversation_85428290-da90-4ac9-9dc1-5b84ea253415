'use client';

import { useState } from 'react';
import { Co<PERSON>, Check, Code, Globe, Key, Book, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

interface IntegrationDocsProps {
  website: {
    _id: string;
    name: string;
    domain: string;
    apiKey: string;
  };
}

export function IntegrationDocs({ website }: IntegrationDocsProps) {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCode(label);
      toast.success(`${label} copied to clipboard!`);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://creasoft-cms-panel-3f918af5b53d.herokuapp.com';

  const codeExamples = {
    fetchBlogs: {
      javascript: `// Fetch all blogs for your website (includes full content)
const response = await fetch('${apiBaseUrl}/api/v1/public/blogs', {
  headers: {
    'X-API-Key': '${website.apiKey}'
  }
});
const data = await response.json();
const blogs = data.data;

// Each blog contains: title, content, excerpt, featuredImage, author, etc.
console.log('Total blogs:', data.total);
console.log('First blog content:', blogs[0]?.content);`,
      
      react: `import { useState, useEffect } from 'react';

function BlogList() {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        const response = await fetch('${apiBaseUrl}/api/v1/public/blogs', {
          headers: {
            'X-API-Key': '${website.apiKey}'
          }
        });

        if (!response.ok) {
          throw new Error(\`HTTP error! status: \${response.status}\`);
        }

        const data = await response.json();
        setBlogs(data.data);
      } catch (error) {
        console.error('Failed to fetch blogs:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  if (loading) return <div className="text-center p-4">Loading blogs...</div>;
  if (error) return <div className="text-red-500 p-4">Error: {error}</div>;
  if (blogs.length === 0) return <div className="text-gray-500 p-4">No blogs found</div>;

  return (
    <div className="space-y-8">
      {blogs.map(blog => (
        <article key={blog.id} className="border-b pb-8">
          <header className="mb-4">
            <h2 className="text-2xl font-bold mb-2">{blog.title}</h2>
            <div className="text-gray-600 text-sm">
              By {blog.author?.name} • {new Date(blog.publishedAt).toLocaleDateString()}
              • {blog.readingTime} min read
            </div>
          </header>

          {blog.featuredImage && (
            <img
              src={blog.featuredImage}
              alt={blog.title}
              className="w-full h-64 object-cover rounded-lg mb-4"
            />
          )}

          <div
            className="prose max-w-none"
            dangerouslySetInnerHTML={{ __html: blog.content }}
          />

          <footer className="mt-4 flex items-center gap-4 text-sm text-gray-500">
            <span>👁 {blog.viewCount} views</span>
            <span>📤 {blog.shareCount} shares</span>
            {blog.categories.length > 0 && (
              <span>🏷 {blog.categories.join(', ')}</span>
            )}
          </footer>
        </article>
      ))}
    </div>
  );
}`,

      nextjs: `// pages/blog/index.js (Pages Router)
export async function getStaticProps() {
  try {
    const response = await fetch('${apiBaseUrl}/api/v1/public/blogs', {
      headers: {
        'X-API-Key': '${website.apiKey}'
      }
    });

    if (!response.ok) {
      throw new Error(\`Failed to fetch blogs: \${response.status}\`);
    }

    const data = await response.json();

    return {
      props: {
        blogs: data.data,
        total: data.total,
      },
      revalidate: 60, // Revalidate every minute
    };
  } catch (error) {
    console.error('Error fetching blogs:', error);
    return {
      props: {
        blogs: [],
        total: 0,
      },
      revalidate: 60,
    };
  }
}

export default function BlogPage({ blogs, total }) {
  return (
    <div className="container mx-auto px-4 py-8">
      <header className="mb-8">
        <h1 className="text-4xl font-bold mb-2">Our Blog</h1>
        <p className="text-gray-600">{total} articles published</p>
      </header>

      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {blogs.map(blog => (
          <article key={blog.id} className="bg-white rounded-lg shadow-md overflow-hidden">
            {blog.featuredImage && (
              <img
                src={blog.featuredImage}
                alt={blog.title}
                className="w-full h-48 object-cover"
              />
            )}
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-2">{blog.title}</h2>
              <p className="text-gray-600 mb-4">{blog.excerpt}</p>

              <div className="text-sm text-gray-500 mb-4">
                By {blog.author?.name} • {new Date(blog.publishedAt).toLocaleDateString()}
              </div>

              <div
                className="prose prose-sm"
                dangerouslySetInnerHTML={{ __html: blog.content }}
              />

              <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
                <span>{blog.readingTime} min read</span>
                <span>{blog.viewCount} views</span>
              </div>
            </div>
          </article>
        ))}
      </div>

      {blogs.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No blog posts found.</p>
        </div>
      )}
    </div>
  );
}`
    },

    fetchSingleBlog: {
      javascript: `// Fetch a single blog by slug
const response = await fetch(\`${apiBaseUrl}/api/v1/public/blogs/\${slug}\`, {
  headers: {
    'X-API-Key': '${website.apiKey}'
  }
});
const data = await response.json();
const blog = data.blog;`,

      react: `import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';

function BlogPost() {
  const { slug } = useParams();
  const [blog, setBlog] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        const response = await fetch(\`${apiBaseUrl}/api/v1/public/blogs/\${slug}\`, {
          headers: {
            'X-API-Key': '${website.apiKey}'
          }
        });
        const data = await response.json();
        setBlog(data.blog);
      } catch (error) {
        console.error('Failed to fetch blog:', error);
      } finally {
        setLoading(false);
      }
    };

    if (slug) fetchBlog();
  }, [slug]);

  if (loading) return <div>Loading...</div>;
  if (!blog) return <div>Blog not found</div>;

  return (
    <article>
      <h1>{blog.title}</h1>
      {blog.featuredImage && (
        <img src={blog.featuredImage} alt={blog.title} />
      )}
      <div dangerouslySetInnerHTML={{ __html: blog.content }} />
      <div>
        <p>Published: {new Date(blog.publishedAt).toLocaleDateString()}</p>
        <p>Reading time: {blog.readingTime} min</p>
      </div>
    </article>
  );
}`,

      nextjs: `// pages/blog/[slug].js or app/blog/[slug]/page.js
export async function getStaticPaths() {
  const response = await fetch('${apiBaseUrl}/api/v1/public/blogs', {
    headers: {
      'X-API-Key': '${website.apiKey}'
    }
  });
  const data = await response.json();

  const paths = data.data.map(blog => ({
    params: { slug: blog.slug }
  }));

  return { paths, fallback: 'blocking' };
}

export async function getStaticProps({ params }) {
  const response = await fetch(\`${apiBaseUrl}/api/v1/public/blogs/\${params.slug}\`, {
    headers: {
      'X-API-Key': '${website.apiKey}'
    }
  });
  const data = await response.json();

  return {
    props: {
      blog: data.blog,
    },
    revalidate: 60,
  };
}

export default function BlogPost({ blog }) {
  return (
    <article>
      <h1>{blog.title}</h1>
      {blog.featuredImage && (
        <img src={blog.featuredImage} alt={blog.title} />
      )}
      <div dangerouslySetInnerHTML={{ __html: blog.content }} />
    </article>
  );
}`
    }
  };

  const CopyButton = ({ text, label }: { text: string; label: string }) => (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => copyToClipboard(text, label)}
      className="absolute top-2 right-2 h-8 w-8 p-0"
    >
      {copiedCode === label ? (
        <Check className="h-4 w-4 text-green-600" />
      ) : (
        <Copy className="h-4 w-4" />
      )}
    </Button>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Book className="h-5 w-5" />
        <h2 className="text-xl font-semibold">Integration Documentation</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <Globe className="h-4 w-4" />
              <CardTitle className="text-sm">Website ID</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <code className="text-sm bg-gray-100 p-2 rounded block pr-10">
                {website._id}
              </code>
              <CopyButton text={website._id} label="Website ID" />
            </div>
          </CardContent>
        </Card>

        <Card className="card-dark">
          <CardHeader className="pb-3 border-b border-[#334155]">
            <div className="flex items-center space-x-2">
              <Key className="h-4 w-4 text-[#3B82F6]" />
              <CardTitle className="text-sm text-white">API Key</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="relative">
              <code className="text-sm bg-[#1A1A2E] text-[#CBD5E1] p-3 rounded-lg block pr-12 border border-[#334155] font-mono">
                {website.apiKey}
              </code>
              <CopyButton text={website.apiKey} label="API Key" />
            </div>
          </CardContent>
        </Card>

        <Card className="card-dark">
          <CardHeader className="pb-3 border-b border-[#334155]">
            <div className="flex items-center space-x-2">
              <ExternalLink className="h-4 w-4 text-[#3B82F6]" />
              <CardTitle className="text-sm text-white">API Base URL</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="relative">
              <code className="text-sm bg-[#1A1A2E] text-[#CBD5E1] p-3 rounded-lg block pr-12 border border-[#334155] font-mono">
                {apiBaseUrl}
              </code>
              <CopyButton text={apiBaseUrl} label="API Base URL" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="card-dark">
        <CardHeader className="border-b border-[#334155]">
          <CardTitle className="text-white">Quick Start Guide</CardTitle>
          <CardDescription className="text-[#94A3B8]">
            Follow these steps to integrate your blog into your website
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-6">
            <div className="flex items-start space-x-3">
              <Badge variant="default" className="mt-0.5 bg-[#3B82F6] text-white">1</Badge>
              <div>
                <h4 className="font-medium text-white mb-2">Choose your integration method</h4>
                <p className="text-sm text-[#CBD5E1]">
                  Use our public API endpoints to fetch blog data. Include your API key in the X-API-Key header for authentication.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Badge variant="default" className="mt-0.5 bg-[#3B82F6] text-white">2</Badge>
              <div>
                <h4 className="font-medium text-white mb-2">Fetch your blogs</h4>
                <p className="text-sm text-[#CBD5E1]">
                  Use your API key to fetch all published blogs for your website.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Badge variant="default" className="mt-0.5 bg-[#3B82F6] text-white">3</Badge>
              <div>
                <h4 className="font-medium text-white mb-2">Render the content</h4>
                <p className="text-sm text-[#CBD5E1]">
                  Display the blog content using your preferred framework and styling. The content includes full HTML formatting.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Badge variant="default" className="mt-0.5 bg-[#3B82F6] text-white">4</Badge>
              <div>
                <h4 className="font-medium text-white mb-2">Handle pagination and filtering</h4>
                <p className="text-sm text-[#CBD5E1]">
                  Use query parameters to implement pagination, search, and filtering by categories or tags.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Badge variant="default" className="mt-0.5 bg-[#3B82F6] text-white">5</Badge>
              <div>
                <h4 className="font-medium text-white mb-2">Implement SEO best practices</h4>
                <p className="text-sm text-[#CBD5E1]">
                  Use the provided SEO metadata for better search engine optimization and social media sharing.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="card-dark">
        <CardHeader className="border-b border-[#334155]">
          <CardTitle className="text-white">Code Examples</CardTitle>
          <CardDescription className="text-[#94A3B8]">
            Ready-to-use code snippets for different frameworks
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <Tabs defaultValue="fetch-blogs" className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-[#1A1A2E] border border-[#334155]">
              <TabsTrigger value="fetch-blogs" className="text-[#CBD5E1] data-[state=active]:bg-[#3B82F6] data-[state=active]:text-white">Fetch All Blogs</TabsTrigger>
              <TabsTrigger value="fetch-single" className="text-[#CBD5E1] data-[state=active]:bg-[#3B82F6] data-[state=active]:text-white">Fetch Single Blog</TabsTrigger>
            </TabsList>

            <TabsContent value="fetch-blogs" className="space-y-4">
              <Tabs defaultValue="javascript" className="w-full">
                <TabsList>
                  <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                  <TabsTrigger value="react">React</TabsTrigger>
                  <TabsTrigger value="nextjs">Next.js</TabsTrigger>
                </TabsList>

                <TabsContent value="javascript">
                  <div className="relative">
                    <pre className="bg-[#0F0F23] text-[#F8FAFC] p-4 rounded-lg overflow-x-auto text-sm border border-[#334155] font-mono">
                      <code className="text-[#F8FAFC]">{codeExamples.fetchBlogs.javascript}</code>
                    </pre>
                    <CopyButton text={codeExamples.fetchBlogs.javascript} label="JavaScript code" />
                  </div>
                </TabsContent>

                <TabsContent value="react">
                  <div className="relative">
                    <pre className="bg-[#0F0F23] text-[#F8FAFC] p-4 rounded-lg overflow-x-auto text-sm border border-[#334155] font-mono">
                      <code className="text-[#F8FAFC]">{codeExamples.fetchBlogs.react}</code>
                    </pre>
                    <CopyButton text={codeExamples.fetchBlogs.react} label="React code" />
                  </div>
                </TabsContent>

                <TabsContent value="nextjs">
                  <div className="relative">
                    <pre className="bg-[#0F0F23] text-[#F8FAFC] p-4 rounded-lg overflow-x-auto text-sm border border-[#334155] font-mono">
                      <code className="text-[#F8FAFC]">{codeExamples.fetchBlogs.nextjs}</code>
                    </pre>
                    <CopyButton text={codeExamples.fetchBlogs.nextjs} label="Next.js code" />
                  </div>
                </TabsContent>
              </Tabs>
            </TabsContent>

            <TabsContent value="fetch-single" className="space-y-4">
              <Tabs defaultValue="javascript" className="w-full">
                <TabsList>
                  <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                  <TabsTrigger value="react">React</TabsTrigger>
                  <TabsTrigger value="nextjs">Next.js</TabsTrigger>
                </TabsList>

                <TabsContent value="javascript">
                  <div className="relative">
                    <pre className="bg-[#0F0F23] text-[#F8FAFC] p-4 rounded-lg overflow-x-auto text-sm border border-[#334155] font-mono">
                      <code className="text-[#F8FAFC]">{codeExamples.fetchSingleBlog.javascript}</code>
                    </pre>
                    <CopyButton text={codeExamples.fetchSingleBlog.javascript} label="JavaScript code" />
                  </div>
                </TabsContent>

                <TabsContent value="react">
                  <div className="relative">
                    <pre className="bg-[#0F0F23] text-[#F8FAFC] p-4 rounded-lg overflow-x-auto text-sm border border-[#334155] font-mono">
                      <code className="text-[#F8FAFC]">{codeExamples.fetchSingleBlog.react}</code>
                    </pre>
                    <CopyButton text={codeExamples.fetchSingleBlog.react} label="React code" />
                  </div>
                </TabsContent>

                <TabsContent value="nextjs">
                  <div className="relative">
                    <pre className="bg-[#0F0F23] text-[#F8FAFC] p-4 rounded-lg overflow-x-auto text-sm border border-[#334155] font-mono">
                      <code className="text-[#F8FAFC]">{codeExamples.fetchSingleBlog.nextjs}</code>
                    </pre>
                    <CopyButton text={codeExamples.fetchSingleBlog.nextjs} label="Next.js code" />
                  </div>
                </TabsContent>
              </Tabs>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>API Endpoints</CardTitle>
          <CardDescription>
            Available public endpoints for fetching blog data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">GET</Badge>
                <code className="text-sm">/api/v1/public/blogs</code>
              </div>
              <p className="text-sm text-gray-600 mb-2">Fetch all published blogs for a website</p>
              <div className="text-sm">
                <strong>Headers:</strong>
                <ul className="list-disc list-inside mt-1 mb-2">
                  <li><code>X-API-Key</code> (required) - Your website API key</li>
                </ul>
                <strong>Query Parameters:</strong>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li><code>page</code> (optional) - Page number for pagination</li>
                  <li><code>limit</code> (optional) - Number of blogs per page</li>
                  <li><code>search</code> (optional) - Search term</li>
                  <li><code>category</code> (optional) - Filter by category</li>
                  <li><code>tags</code> (optional) - Filter by tags (comma-separated)</li>
                  <li><code>featured</code> (optional) - Include only featured blogs</li>
                  <li><code>sortBy</code> (optional) - Sort field (publishedAt, title, viewCount)</li>
                  <li><code>sortOrder</code> (optional) - Sort order (asc, desc)</li>
                </ul>
              </div>
            </div>

            <div className="border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">GET</Badge>
                <code className="text-sm">/api/v1/public/blogs/:slug</code>
              </div>
              <p className="text-sm text-gray-600 mb-2">Fetch a single blog by its slug</p>
              <div className="text-sm">
                <strong>Headers:</strong>
                <ul className="list-disc list-inside mt-1">
                  <li><code>X-API-Key</code> (required) - Your website API key</li>
                </ul>
              </div>
            </div>

            <div className="border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">POST</Badge>
                <code className="text-sm">/api/v1/public/blogs/:id/share</code>
              </div>
              <p className="text-sm text-gray-600 mb-2">Increment share count for a blog</p>
              <div className="text-sm">
                <strong>Headers:</strong>
                <ul className="list-disc list-inside mt-1">
                  <li><code>X-API-Key</code> (required) - Your website API key</li>
                </ul>
              </div>
            </div>

            <div className="border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">GET</Badge>
                <code className="text-sm">/api/v1/public/categories</code>
              </div>
              <p className="text-sm text-gray-600 mb-2">Get all available categories for the website</p>
              <div className="text-sm">
                <strong>Headers:</strong>
                <ul className="list-disc list-inside mt-1">
                  <li><code>X-API-Key</code> (required) - Your website API key</li>
                </ul>
              </div>
            </div>

            <div className="border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">GET</Badge>
                <code className="text-sm">/api/v1/public/tags</code>
              </div>
              <p className="text-sm text-gray-600 mb-2">Get all available tags for the website</p>
              <div className="text-sm">
                <strong>Headers:</strong>
                <ul className="list-disc list-inside mt-1">
                  <li><code>X-API-Key</code> (required) - Your website API key</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Response Format</CardTitle>
          <CardDescription>
            Understanding the API response structure
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Blog List Response</h4>
              <div className="relative">
                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{`{
  "data": [
    {
      "id": "blog_id",
      "title": "Blog Title",
      "slug": "blog-title",
      "content": "<h1>Full HTML Content</h1><p>Complete blog content with HTML formatting...</p>",
      "excerpt": "Brief description...",
      "featuredImage": "https://example.com/image.jpg",
      "publishedAt": "2024-01-01T00:00:00.000Z",
      "readingTime": 5,
      "viewCount": 42,
      "shareCount": 8,
      "isFeatured": false,
      "categories": ["Technology", "Web Development"],
      "tags": ["javascript", "api", "tutorial"],
      "author": {
        "firstName": "John",
        "lastName": "Doe",
        "name": "John Doe"
      },
      "seo": {
        "title": "Blog Title - SEO Optimized",
        "description": "SEO meta description...",
        "ogImage": "https://example.com/og-image.jpg",
        "noIndex": false,
        "noFollow": false
      }
    }
  ],
  "total": 10,
  "page": 1,
  "limit": 10,
  "totalPages": 1,
  "hasNext": false,
  "hasPrev": false,
  "message": "Blogs retrieved successfully"
}`}</code>
                </pre>
                <CopyButton text={`{
  "data": [
    {
      "_id": "blog_id",
      "title": "Blog Title",
      "slug": "blog-title",
      "excerpt": "Brief description...",
      "content": "<p>Full HTML content...</p>",
      "featuredImage": "https://...",
      "publishedAt": "2024-01-01T00:00:00.000Z",
      "readingTime": 5,
      "categories": ["category1", "category2"],
      "tags": ["tag1", "tag2"],
      "author": {
        "firstName": "John",
        "lastName": "Doe"
      }
    }
  ],
  "total": 10,
  "page": 1,
  "limit": 10,
  "totalPages": 1,
  "hasNext": false,
  "hasPrev": false
}`} label="Response format" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Practical Examples</CardTitle>
          <CardDescription>
            Common implementation patterns and use cases
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <h4 className="font-medium mb-2">Pagination Example</h4>
              <div className="relative">
                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{`// Fetch blogs with pagination
const page = 1;
const limit = 6;
const response = await fetch(\`${apiBaseUrl}/api/v1/public/blogs?page=\${page}&limit=\${limit}\`, {
  headers: { 'X-API-Key': '${website.apiKey}' }
});
const data = await response.json();

console.log(\`Showing \${data.data.length} of \${data.total} blogs\`);
console.log(\`Page \${data.page} of \${data.totalPages}\`);
console.log(\`Has next page: \${data.hasNext}\`);`}</code>
                </pre>
                <CopyButton text={`// Fetch blogs with pagination
const page = 1;
const limit = 6;
const response = await fetch(\`${apiBaseUrl}/api/v1/public/blogs?page=\${page}&limit=\${limit}\`, {
  headers: { 'X-API-Key': '${website.apiKey}' }
});
const data = await response.json();

console.log(\`Showing \${data.data.length} of \${data.total} blogs\`);
console.log(\`Page \${data.page} of \${data.totalPages}\`);
console.log(\`Has next page: \${data.hasNext}\`);`} label="Pagination example" />
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Search and Filter Example</h4>
              <div className="relative">
                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{`// Search blogs and filter by category
const searchTerm = 'javascript';
const category = 'Technology';
const response = await fetch(\`${apiBaseUrl}/api/v1/public/blogs?search=\${searchTerm}&category=\${category}&featured=true\`, {
  headers: { 'X-API-Key': '${website.apiKey}' }
});
const data = await response.json();

// Filter featured blogs only
const featuredBlogs = data.data.filter(blog => blog.isFeatured);`}</code>
                </pre>
                <CopyButton text={`// Search blogs and filter by category
const searchTerm = 'javascript';
const category = 'Technology';
const response = await fetch(\`${apiBaseUrl}/api/v1/public/blogs?search=\${searchTerm}&category=\${category}&featured=true\`, {
  headers: { 'X-API-Key': '${website.apiKey}' }
});
const data = await response.json();

// Filter featured blogs only
const featuredBlogs = data.data.filter(blog => blog.isFeatured);`} label="Search and filter example" />
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">SEO Implementation Example</h4>
              <div className="relative">
                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{`// Generate SEO meta tags for a blog
function generateSEOTags(blog) {
  return \`
    <title>\${blog.seo.title}</title>
    <meta name="description" content="\${blog.seo.description}" />
    <meta property="og:title" content="\${blog.seo.title}" />
    <meta property="og:description" content="\${blog.seo.description}" />
    <meta property="og:image" content="\${blog.seo.ogImage}" />
    <meta property="og:type" content="article" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="\${blog.seo.title}" />
    <meta name="twitter:description" content="\${blog.seo.description}" />
    <meta name="twitter:image" content="\${blog.seo.ogImage}" />
  \`;
}`}</code>
                </pre>
                <CopyButton text={`// Generate SEO meta tags for a blog
function generateSEOTags(blog) {
  return \`
    <title>\${blog.seo.title}</title>
    <meta name="description" content="\${blog.seo.description}" />
    <meta property="og:title" content="\${blog.seo.title}" />
    <meta property="og:description" content="\${blog.seo.description}" />
    <meta property="og:image" content="\${blog.seo.ogImage}" />
    <meta property="og:type" content="article" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="\${blog.seo.title}" />
    <meta name="twitter:description" content="\${blog.seo.description}" />
    <meta name="twitter:image" content="\${blog.seo.ogImage}" />
  \`;
}`} label="SEO implementation example" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Error Handling & Best Practices</CardTitle>
          <CardDescription>
            Tips for robust integration and common pitfalls to avoid
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-green-600 mb-2">✅ Best Practices</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                <li>Always include error handling for API requests</li>
                <li>Implement loading states for better user experience</li>
                <li>Cache API responses when possible to reduce server load</li>
                <li>Use pagination for better performance with large blog collections</li>
                <li>Sanitize HTML content if you're not using dangerouslySetInnerHTML</li>
                <li>Implement proper SEO meta tags using the provided SEO data</li>
                <li>Use the share endpoint to track blog engagement</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-red-600 mb-2">❌ Common Pitfalls</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                <li>Forgetting to include the X-API-Key header (results in 401 error)</li>
                <li>Not handling empty blog lists gracefully</li>
                <li>Displaying raw HTML without proper styling</li>
                <li>Not implementing responsive design for featured images</li>
                <li>Ignoring the pagination metadata for large datasets</li>
                <li>Not validating API responses before using the data</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-blue-600 mb-2">🔧 Error Handling Example</h4>
              <div className="relative">
                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{`async function fetchBlogsWithErrorHandling() {
  try {
    const response = await fetch('${apiBaseUrl}/api/v1/public/blogs', {
      headers: { 'X-API-Key': '${website.apiKey}' }
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Invalid API key');
      } else if (response.status === 404) {
        throw new Error('Blogs not found');
      } else {
        throw new Error(\`HTTP error! status: \${response.status}\`);
      }
    }

    const data = await response.json();

    if (!data.data || !Array.isArray(data.data)) {
      throw new Error('Invalid response format');
    }

    return data;
  } catch (error) {
    console.error('Failed to fetch blogs:', error);
    return { data: [], total: 0, error: error.message };
  }
}`}</code>
                </pre>
                <CopyButton text={`async function fetchBlogsWithErrorHandling() {
  try {
    const response = await fetch('${apiBaseUrl}/api/v1/public/blogs', {
      headers: { 'X-API-Key': '${website.apiKey}' }
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Invalid API key');
      } else if (response.status === 404) {
        throw new Error('Blogs not found');
      } else {
        throw new Error(\`HTTP error! status: \${response.status}\`);
      }
    }

    const data = await response.json();

    if (!data.data || !Array.isArray(data.data)) {
      throw new Error('Invalid response format');
    }

    return data;
  } catch (error) {
    console.error('Failed to fetch blogs:', error);
    return { data: [], total: 0, error: error.message };
  }
}`} label="Error handling example" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
