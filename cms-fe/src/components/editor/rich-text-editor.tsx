'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import TextAlign from '@tiptap/extension-text-align';
import Underline from '@tiptap/extension-underline';
import Color from '@tiptap/extension-color';
import { TextStyle } from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import FontSize from '@tiptap/extension-font-size';
import { useCallback, useState } from 'react';
import {
  Bold,
  Italic,
  Strikethrough,
  Code,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  Link as LinkIcon,
  Image as ImageIcon,
  Type,
  Loader2,
  AlertCircle,
  Underline as UnderlineIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Indent,
  Outdent,
  Palette,
  Sparkles,
  ChevronDown,
  MoreHorizontal,
  Video,
  FileImage,
  Grid3X3,
  Upload
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  onImageUpload?: (file: File) => Promise<string>;
}

export function RichTextEditor({
  content,
  onChange,
  placeholder = 'Start writing your blog post...',
  className,
  onImageUpload
}: RichTextEditorProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [showMediaPanel, setShowMediaPanel] = useState(false);
  const [fontSize, setFontSize] = useState('18');

  // Get current active format
  const getActiveFormat = () => {
    if (!editor) return 'paragraph';

    if (editor.isActive('heading', { level: 1 })) return 'heading1';
    if (editor.isActive('heading', { level: 2 })) return 'heading2';
    if (editor.isActive('heading', { level: 3 })) return 'heading3';
    if (editor.isActive('heading', { level: 4 })) return 'heading4';
    if (editor.isActive('heading', { level: 5 })) return 'heading5';
    if (editor.isActive('heading', { level: 6 })) return 'heading6';

    return 'paragraph';
  };

  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextStyle,
      Color,
      FontFamily,
      FontSize,
      HorizontalRule,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
        allowBase64: true,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
    ],
    immediatelyRender: false,
    content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none min-h-[500px] p-8 text-white leading-relaxed prose-invert',
      },
      handleDrop: (view, event, slice, moved) => {
        if (!moved && event.dataTransfer && event.dataTransfer.files && event.dataTransfer.files[0]) {
          const file = event.dataTransfer.files[0];
          if (file.type.includes('image/') && onImageUpload) {
            event.preventDefault();
            onImageUpload(file).then((url) => {
              const { schema } = view.state;
              const coordinates = view.posAtCoords({ left: event.clientX, top: event.clientY });
              if (coordinates) {
                const node = schema.nodes.image.create({ src: url });
                const transaction = view.state.tr.insert(coordinates.pos, node);
                view.dispatch(transaction);
              }
            }).catch((error) => {
              console.error('Failed to upload image:', error);
            });
            return true;
          }
        }
        return false;
      },
      handlePaste: (view, event, slice) => {
        const items = Array.from(event.clipboardData?.items || []);
        const imageItem = items.find(item => item.type.includes('image/'));

        if (imageItem && onImageUpload) {
          event.preventDefault();
          const file = imageItem.getAsFile();
          if (file) {
            onImageUpload(file).then((url) => {
              const { schema } = view.state;
              const node = schema.nodes.image.create({ src: url });
              const transaction = view.state.tr.replaceSelectionWith(node);
              view.dispatch(transaction);
            }).catch((error) => {
              console.error('Failed to upload image:', error);
            });
          }
          return true;
        }
        return false;
      },
    },
  });

  const addImage = useCallback(async () => {
    if (!onImageUpload) {
      const url = window.prompt('Enter image URL:');
      if (url) {
        editor?.chain().focus().setImage({ src: url }).run();
      }
      return;
    }

    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        setIsUploading(true);
        try {
          const url = await onImageUpload(file);
          editor?.chain().focus().setImage({ src: url }).run();
          toast.success('Image uploaded successfully!');
        } catch (error) {
          console.error('Failed to upload image:', error);
          toast.error('Failed to upload image. Please try again.');
        } finally {
          setIsUploading(false);
        }
      }
    };
    input.click();
  }, [editor, onImageUpload]);

  const addLink = () => {
    const previousUrl = editor?.getAttributes('link').href;
    const url = window.prompt('Enter URL:', previousUrl);

    if (url === null) {
      return;
    }

    if (url === '') {
      editor?.chain().focus().extendMarkRange('link').unsetLink().run();
      return;
    }

    editor?.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
  };

  if (!editor) {
    return null;
  }

  return (
    <div className={cn('bg-[#1E293B] rounded-lg shadow-sm border border-[#334155]', className)}>
      {/* Enhanced Wix-style Toolbar */}
      <div className="border-b border-[#334155] p-3">
        <div className="flex items-center gap-2 flex-wrap">
          {/* Content AI */}
          <Button
            variant="ghost"
            size="sm"
            className="text-[#3B82F6] hover:bg-[#3B82F6]/10 flex items-center gap-1 px-3"
          >
            <Sparkles className="h-4 w-4" />
            Content AI
          </Button>

          <div className="w-px h-6 bg-[#475569] mx-1" />

          {/* Text Format Dropdown */}
          <div className="relative">
            <select
              value={getActiveFormat()}
              onChange={(e) => {
                const value = e.target.value;
                if (value === 'paragraph') {
                  editor?.chain().focus().setParagraph().run();
                } else if (value.startsWith('heading')) {
                  const level = parseInt(value.replace('heading', '')) as 1 | 2 | 3 | 4 | 5 | 6;
                  editor?.chain().focus().setHeading({ level }).run();
                }
              }}
              className="appearance-none bg-[#334155] border border-[#475569] rounded px-2 py-1 text-sm font-medium text-white pr-6 focus:outline-none cursor-pointer focus:ring-2 focus:ring-[#3B82F6]"
            >
              <option value="paragraph">Paragraph</option>
              <option value="heading1">Heading 1</option>
              <option value="heading2">Heading 2</option>
              <option value="heading3">Heading 3</option>
              <option value="heading4">Heading 4</option>
              <option value="heading5">Heading 5</option>
              <option value="heading6">Heading 6</option>
            </select>
            <ChevronDown className="absolute right-1 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#94A3B8] pointer-events-none" />
          </div>

          {/* Font Size */}
          <div className="relative">
            <select
              value={fontSize}
              onChange={(e) => {
                const newSize = e.target.value;
                setFontSize(newSize);
                // Apply font size to current selection or next typed text
                editor?.chain().focus().setFontSize(newSize).run();
              }}
              className="appearance-none bg-[#334155] border border-[#475569] rounded px-2 py-1 text-sm font-medium text-white pr-6 focus:outline-none cursor-pointer focus:ring-2 focus:ring-[#3B82F6]"
            >
              <option value="12">12</option>
              <option value="14">14</option>
              <option value="16">16</option>
              <option value="18">18</option>
              <option value="20">20</option>
              <option value="24">24</option>
              <option value="28">28</option>
              <option value="32">32</option>
            </select>
            <ChevronDown className="absolute right-1 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#94A3B8] pointer-events-none" />
          </div>

          <div className="w-px h-6 bg-[#475569] mx-1" />

          {/* Text Formatting */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor?.chain().focus().toggleBold().run()}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive('bold') && 'bg-[#3B82F6] text-white'
              )}
            >
              <Bold className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor?.chain().focus().toggleItalic().run()}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive('italic') && 'bg-[#3B82F6] text-white'
              )}
            >
              <Italic className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor?.chain().focus().toggleUnderline().run()}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive('underline') && 'bg-[#3B82F6] text-white'
              )}
            >
              <UnderlineIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor?.chain().focus().toggleStrike().run()}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive('strike') && 'bg-[#3B82F6] text-white'
              )}
            >
              <Strikethrough className="h-4 w-4" />
            </Button>
          </div>

          {/* Text Color */}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]"
          >
            <Palette className="h-4 w-4" />
          </Button>

          <div className="w-px h-6 bg-[#475569] mx-1" />

          {/* Link, Code, and Media */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={addLink}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive('link') && 'bg-[#3B82F6] text-white'
              )}
            >
              <LinkIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor?.chain().focus().toggleCode().run()}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive('code') && 'bg-[#3B82F6] text-white'
              )}
            >
              <Code className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowMediaPanel(!showMediaPanel)}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                showMediaPanel && 'bg-[#3B82F6] text-white'
              )}
            >
              <ImageIcon className="h-4 w-4" />
            </Button>
          </div>

          <div className="w-px h-6 bg-[#475569] mx-1" />

          {/* Lists */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor?.chain().focus().toggleBulletList().run()}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive('bulletList') && 'bg-[#3B82F6] text-white'
              )}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor?.chain().focus().toggleOrderedList().run()}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive('orderedList') && 'bg-[#3B82F6] text-white'
              )}
            >
              <ListOrdered className="h-4 w-4" />
            </Button>
          </div>

          <div className="w-px h-6 bg-[#475569] mx-1" />

          {/* Text Alignment */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor?.chain().focus().setTextAlign('left').run()}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive({ textAlign: 'left' }) && 'bg-[#3B82F6] text-white'
              )}
            >
              <AlignLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor?.chain().focus().setTextAlign('center').run()}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive({ textAlign: 'center' }) && 'bg-[#3B82F6] text-white'
              )}
            >
              <AlignCenter className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor?.chain().focus().setTextAlign('right').run()}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive({ textAlign: 'right' }) && 'bg-[#3B82F6] text-white'
              )}
            >
              <AlignRight className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor?.chain().focus().setTextAlign('justify').run()}
              className={cn(
                'h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]',
                editor?.isActive({ textAlign: 'justify' }) && 'bg-[#3B82F6] text-white'
              )}
            >
              <AlignJustify className="h-4 w-4" />
            </Button>
          </div>

          <div className="w-px h-6 bg-[#475569] mx-1" />

          {/* Indent Controls */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]"
            >
              <Outdent className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]"
            >
              <Indent className="h-4 w-4" />
            </Button>
          </div>

          <div className="w-px h-6 bg-[#475569] mx-1" />

          {/* More Options */}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-[#334155] text-[#CBD5E1]"
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="relative">
        <EditorContent editor={editor} />

        {/* Enhanced Media Panel */}
        {showMediaPanel && (
          <div className="absolute top-4 left-4 bg-[#1E293B] rounded-lg shadow-xl border border-[#334155] p-6 z-20 w-96">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white">Add</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMediaPanel(false)}
                className="h-8 w-8 p-0 text-[#94A3B8] hover:text-white hover:bg-[#334155]"
              >
                ×
              </Button>
            </div>

            <div className="mb-6">
              <h4 className="text-sm font-medium text-[#CBD5E1] mb-3">Media</h4>
              <div className="grid grid-cols-3 gap-3">
                <Button
                  variant="outline"
                  className="h-24 flex flex-col items-center justify-center gap-2 border-[#334155] text-[#CBD5E1] hover:bg-[#3B82F6]/10 hover:border-[#3B82F6] transition-colors"
                  onClick={addImage}
                  disabled={isUploading}
                >
                  {isUploading ? (
                    <Loader2 className="h-8 w-8 animate-spin text-[#3B82F6]" />
                  ) : (
                    <FileImage className="h-8 w-8 text-[#CBD5E1]" />
                  )}
                  <span className="text-xs font-medium">Image</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-24 flex flex-col items-center justify-center gap-2 border-[#334155] text-[#CBD5E1] hover:bg-[#3B82F6]/10 hover:border-[#3B82F6] transition-colors"
                >
                  <Grid3X3 className="h-8 w-8 text-[#CBD5E1]" />
                  <span className="text-xs font-medium">Gallery</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-24 flex flex-col items-center justify-center gap-2 border-[#334155] text-[#CBD5E1] hover:bg-[#3B82F6]/10 hover:border-[#3B82F6] transition-colors"
                >
                  <Video className="h-8 w-8 text-[#CBD5E1]" />
                  <span className="text-xs font-medium">Video</span>
                </Button>
              </div>
            </div>

            <div className="mb-6">
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  className="h-24 flex flex-col items-center justify-center gap-2 border-[#334155] text-[#CBD5E1] hover:bg-[#3B82F6]/10 hover:border-[#3B82F6] transition-colors"
                >
                  <div className="text-2xl">🎬</div>
                  <span className="text-xs font-medium">GIF</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-24 flex flex-col items-center justify-center gap-2 border-[#334155] text-[#CBD5E1] hover:bg-[#3B82F6]/10 hover:border-[#3B82F6] transition-colors"
                >
                  <Upload className="h-8 w-8 text-[#CBD5E1]" />
                  <span className="text-xs font-medium">File</span>
                </Button>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="border-t border-[#334155] pt-4">
              <h4 className="text-sm font-medium text-[#CBD5E1] mb-3">Quick Insert</h4>
              <div className="space-y-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-left hover:bg-[#334155] text-[#CBD5E1]"
                  onClick={() => {
                    editor?.chain().focus().toggleBlockquote().run();
                    setShowMediaPanel(false);
                  }}
                >
                  <Quote className="h-4 w-4 mr-2" />
                  Quote
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-left hover:bg-[#334155] text-[#CBD5E1]"
                  onClick={() => {
                    editor?.chain().focus().setHorizontalRule().run();
                    setShowMediaPanel(false);
                  }}
                >
                  <div className="h-4 w-4 mr-2 border-t-2 border-[#94A3B8]"></div>
                  Divider
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
