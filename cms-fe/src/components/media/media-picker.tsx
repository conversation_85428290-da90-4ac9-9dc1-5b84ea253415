'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { X, Upload, Image, Search, Grid, List, Check } from 'lucide-react';
import { useMedia, useUploadMedia } from '@/hooks/use-media';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import toast from 'react-hot-toast';

interface MediaPickerProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (mediaUrl: string) => void;
  selectedUrl?: string;
  allowUpload?: boolean;
  acceptedTypes?: string[];
}

interface MediaItem {
  _id: string;
  url: string;
  originalName: string;
  alt?: string;
  type: string;
  size: number;
  createdAt: string;
}

export function MediaPicker({
  isOpen,
  onClose,
  onSelect,
  selectedUrl,
  allowUpload = true,
  acceptedTypes = ['image/*']
}: MediaPickerProps) {
  const [search, setSearch] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isUploading, setIsUploading] = useState(false);

  const { data: mediaData, isLoading, refetch } = useMedia({ 
    search, 
    type: acceptedTypes.includes('image/*') ? 'image' : undefined 
  });
  const uploadMedia = useUploadMedia();

  const media = mediaData?.data || [];

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!allowUpload) return;
    
    setIsUploading(true);
    let successCount = 0;

    for (const file of acceptedFiles) {
      try {
        const formData = new FormData();
        formData.append('file', file);
        
        await uploadMedia.mutateAsync(formData);
        successCount++;
      } catch (error) {
        console.error('Upload failed:', error);
      }
    }

    if (successCount > 0) {
      toast.success(`${successCount} file(s) uploaded successfully!`);
      refetch();
    }
    
    setIsUploading(false);
  }, [allowUpload, uploadMedia, refetch]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    disabled: !allowUpload || isUploading,
  });

  const handleSelect = (url: string) => {
    onSelect(url);
    onClose();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Image className="h-5 w-5 mr-2" />
            Select Media
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Upload Area */}
          {allowUpload && (
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <input {...getInputProps()} />
              <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600">
                {isDragActive
                  ? 'Drop files here...'
                  : 'Drag & drop files here, or click to select'}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Supported: {acceptedTypes.join(', ')}
              </p>
            </div>
          )}

          {/* Search and View Controls */}
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Input
                placeholder="Search media..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Media Grid/List */}
          <div className="max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="text-sm text-gray-500 mt-2">Loading media...</p>
              </div>
            ) : media.length > 0 ? (
              <div className={
                viewMode === 'grid'
                  ? 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4'
                  : 'space-y-2'
              }>
                {media.map((item: MediaItem) => (
                  <MediaPickerItem
                    key={item._id}
                    media={item}
                    isSelected={selectedUrl === item.url}
                    onSelect={() => handleSelect(item.url)}
                    viewMode={viewMode}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Image className="h-12 w-12 mx-auto text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">
                  {search ? 'No media found matching your search' : 'No media files yet'}
                </p>
                {allowUpload && !search && (
                  <p className="text-xs text-gray-400 mt-1">
                    Upload some files to get started
                  </p>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

interface MediaPickerItemProps {
  media: MediaItem;
  isSelected: boolean;
  onSelect: () => void;
  viewMode: 'grid' | 'list';
}

function MediaPickerItem({ media, isSelected, onSelect, viewMode }: MediaPickerItemProps) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (viewMode === 'list') {
    return (
      <div
        className={`flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
          isSelected ? 'bg-blue-50 border-blue-200' : 'border-gray-200'
        }`}
        onClick={onSelect}
      >
        <div className="relative">
          <img
            src={media.url}
            alt={media.alt || media.originalName}
            className="w-12 h-12 object-cover rounded"
          />
          {isSelected && (
            <div className="absolute -top-1 -right-1 bg-blue-500 text-white rounded-full p-1">
              <Check className="h-3 w-3" />
            </div>
          )}
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium truncate">{media.originalName}</p>
          <p className="text-xs text-gray-500">{formatFileSize(media.size)}</p>
        </div>
      </div>
    );
  }

  return (
    <Card
      className={`cursor-pointer hover:shadow-md transition-shadow ${
        isSelected ? 'ring-2 ring-blue-500' : ''
      }`}
      onClick={onSelect}
    >
      <CardContent className="p-2">
        <div className="relative">
          <img
            src={media.url}
            alt={media.alt || media.originalName}
            className="w-full h-24 object-cover rounded"
          />
          {isSelected && (
            <div className="absolute top-1 right-1 bg-blue-500 text-white rounded-full p-1">
              <Check className="h-3 w-3" />
            </div>
          )}
        </div>
        <div className="mt-2">
          <p className="text-xs font-medium truncate">{media.originalName}</p>
          <p className="text-xs text-gray-500">{formatFileSize(media.size)}</p>
        </div>
      </CardContent>
    </Card>
  );
}
