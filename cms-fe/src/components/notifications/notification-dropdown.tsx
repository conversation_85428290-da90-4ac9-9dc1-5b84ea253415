'use client';

import { useState } from 'react';
import Link from 'next/link';
import { 
  <PERSON>, 
  <PERSON>, 
  CheckCheck, 
  Trash2, 
  ExternalLink,
  Info,
  CheckCircle,
  AlertTriangle,
  XCircle
} from 'lucide-react';
import { useNotifications, type Notification } from '@/hooks/use-notifications';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { formatRelativeTime } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface NotificationDropdownProps {
  className?: string;
}

function NotificationIcon({ type }: { type: Notification['type'] }) {
  const iconClass = "h-4 w-4";
  
  switch (type) {
    case 'success':
      return <CheckCircle className={cn(iconClass, "text-green-500")} />;
    case 'warning':
      return <AlertTriangle className={cn(iconClass, "text-yellow-500")} />;
    case 'error':
      return <XCircle className={cn(iconClass, "text-red-500")} />;
    default:
      return <Info className={cn(iconClass, "text-blue-500")} />;
  }
}

function NotificationItem({ notification, onMarkAsRead, onDelete }: {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
}) {
  return (
    <div className={cn(
      "p-4 border-b border-[#334155] hover:bg-[#334155] transition-colors group",
      !notification.isRead && "bg-[#1E293B]"
    )}>
      <div className="flex items-start space-x-3">
        <NotificationIcon type={notification.type} />
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className={cn(
              "text-sm font-medium truncate",
              notification.isRead ? "text-[#CBD5E1]" : "text-white"
            )}>
              {notification.title}
            </h4>
            {!notification.isRead && (
              <div className="h-2 w-2 bg-[#3B82F6] rounded-full ml-2 flex-shrink-0" />
            )}
          </div>
          <p className="text-xs text-[#94A3B8] mt-1 line-clamp-2">
            {notification.message}
          </p>
          <div className="flex items-center justify-between mt-2">
            <span className="text-xs text-[#64748B]">
              {formatRelativeTime(notification.createdAt)}
            </span>
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {!notification.isRead && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onMarkAsRead(notification._id)}
                  className="h-6 w-6 p-0 hover:bg-[#475569]"
                >
                  <Check className="h-3 w-3" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(notification._id)}
                className="h-6 w-6 p-0 hover:bg-[#475569] text-red-400 hover:text-red-300"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
              {notification.actionUrl && (
                <Link href={notification.actionUrl}>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-[#475569]"
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function NotificationDropdown({ className }: NotificationDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const {
    notifications,
    isLoading,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
  } = useNotifications();

  return (
    <div className={cn("relative", className)}>
      <Button
        variant="ghost"
        size="icon"
        className="text-[#CBD5E1] hover:text-white hover:bg-[#334155] relative"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute right-0 top-full mt-2 w-80 bg-[#1E293B] border border-[#475569] rounded-lg shadow-xl z-50 max-h-96 overflow-hidden">
            <div className="p-4 border-b border-[#334155] flex items-center justify-between">
              <h3 className="text-white font-medium">Notifications</h3>
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => markAllAsRead()}
                  className="text-[#3B82F6] hover:text-white hover:bg-[#334155] h-7 px-2"
                >
                  <CheckCheck className="h-4 w-4 mr-1" />
                  Mark all read
                </Button>
              )}
            </div>
            
            <div className="max-h-80 overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#3B82F6] mx-auto"></div>
                  <p className="text-[#94A3B8] text-sm mt-2">Loading notifications...</p>
                </div>
              ) : !notifications || notifications.length === 0 ? (
                <div className="p-8 text-center">
                  <Bell className="h-8 w-8 text-[#64748B] mx-auto mb-2" />
                  <p className="text-[#94A3B8] text-sm">No notifications</p>
                </div>
              ) : (
                notifications.map((notification: Notification) => (
                  <NotificationItem
                    key={notification._id}
                    notification={notification}
                    onMarkAsRead={markAsRead}
                    onDelete={deleteNotification}
                  />
                ))
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
