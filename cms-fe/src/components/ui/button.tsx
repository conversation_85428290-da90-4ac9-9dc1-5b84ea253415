'use client';

import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#3B82F6] focus-visible:ring-offset-2 focus-visible:ring-offset-[#0F0F23] disabled:pointer-events-none disabled:opacity-50 overflow-hidden',
  {
    variants: {
      variant: {
        default: 'bg-gradient-to-r from-[#3B82F6] to-[#1E40AF] text-white hover:from-[#1E40AF] hover:to-[#1E3A8A] hover:shadow-lg hover:shadow-[#3B82F6]/25 hover:-translate-y-0.5',
        destructive:
          'bg-gradient-to-r from-[#EF4444] to-[#DC2626] text-white hover:from-[#DC2626] hover:to-[#B91C1C] hover:shadow-lg hover:shadow-[#EF4444]/25',
        outline:
          'border border-[#334155] bg-transparent text-[#CBD5E1] hover:bg-[#334155] hover:text-white hover:border-[#3B82F6]',
        secondary:
          'bg-[#334155] text-[#CBD5E1] hover:bg-[#475569] hover:text-white',
        ghost: 'text-[#CBD5E1] hover:bg-[#334155] hover:text-white',
        link: 'text-[#3B82F6] underline-offset-4 hover:underline hover:text-[#60A5FA]',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-lg px-3',
        lg: 'h-11 rounded-lg px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
