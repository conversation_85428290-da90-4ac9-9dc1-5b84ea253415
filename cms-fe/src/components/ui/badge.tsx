import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#3B82F6] focus:ring-offset-2 focus:ring-offset-[#0F0F23]",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-gradient-to-r from-[#3B82F6] to-[#1E40AF] text-white hover:from-[#1E40AF] hover:to-[#1E3A8A] shadow-sm",
        secondary:
          "border-transparent bg-[#475569] text-[#E2E8F0] hover:bg-[#64748B] hover:text-white font-medium",
        destructive:
          "border-transparent bg-gradient-to-r from-[#EF4444] to-[#DC2626] text-white hover:from-[#DC2626] hover:to-[#B91C1C]",
        outline: "border-[#475569] text-[#E2E8F0] hover:bg-[#475569] hover:text-white font-medium",
        success: "border-transparent bg-[#10B981] text-white hover:bg-[#059669]",
        warning: "border-transparent bg-[#F59E0B] text-white hover:bg-[#D97706]",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }