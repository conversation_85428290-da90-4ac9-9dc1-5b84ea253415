import React from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showText?: boolean;
  className?: string;
}

export function Logo({ size = 'md', showText = true, className = '' }: LogoProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl'
  };

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Logo Icon */}
      <div className={`${sizeClasses[size]} relative`}>
        <svg
          viewBox="0 0 100 100"
          className="w-full h-full"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Background Circle with Gradient */}
          <defs>
            <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#3B82F6" />
              <stop offset="50%" stopColor="#1E40AF" />
              <stop offset="100%" stopColor="#1E3A8A" />
            </linearGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
              <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          
          {/* Background */}
          <circle cx="50" cy="50" r="48" fill="url(#logoGradient)" filter="url(#glow)" />
          
          {/* Hexagonal Pattern - Simplified version of the logo */}
          <g transform="translate(50,50)" fill="white">
            {/* Center hexagon */}
            <polygon 
              points="-8,-14 8,-14 16,0 8,14 -8,14 -16,0" 
              opacity="0.9"
            />
            
            {/* Top segments */}
            <rect x="-20" y="-25" width="12" height="4" rx="2" />
            <rect x="-5" y="-25" width="12" height="4" rx="2" />
            <rect x="10" y="-25" width="12" height="4" rx="2" />
            
            {/* Top-right segments */}
            <rect x="15" y="-15" width="12" height="4" rx="2" transform="rotate(60 21 -13)" />
            <rect x="20" y="-5" width="12" height="4" rx="2" transform="rotate(60 26 -3)" />
            <rect x="15" y="5" width="12" height="4" rx="2" transform="rotate(60 21 7)" />
            
            {/* Bottom-right segments */}
            <rect x="10" y="21" width="12" height="4" rx="2" />
            <rect x="-5" y="21" width="12" height="4" rx="2" />
            <rect x="-20" y="21" width="12" height="4" rx="2" />
            
            {/* Bottom-left segments */}
            <rect x="-27" y="5" width="12" height="4" rx="2" transform="rotate(-60 -21 7)" />
            <rect x="-32" y="-5" width="12" height="4" rx="2" transform="rotate(-60 -26 -3)" />
            <rect x="-27" y="-15" width="12" height="4" rx="2" transform="rotate(-60 -21 -13)" />
          </g>
        </svg>
      </div>

      {/* Brand Text */}
      {showText && (
        <div className="flex flex-col">
          <span className={`font-bold ${textSizeClasses[size]} text-white leading-none`}>
            CreaSoft
          </span>
          <span className={`text-blue-300 ${size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'} leading-none`}>
            CMS
          </span>
        </div>
      )}
    </div>
  );
}

export function LogoIcon({ size = 'md', className = '' }: Omit<LogoProps, 'showText'>) {
  return <Logo size={size} showText={false} className={className} />;
}

export function LogoWithText({ size = 'md', className = '' }: Omit<LogoProps, 'showText'>) {
  return <Logo size={size} showText={true} className={className} />;
}
