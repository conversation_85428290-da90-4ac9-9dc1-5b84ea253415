'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { mediaApi } from '@/lib/api';
import { Media, QueryParams } from '@/types';
import toast from 'react-hot-toast';

export function useMedia(params?: QueryParams & { type?: string; folder?: string }) {
  return useQuery({
    queryKey: ['media', params],
    queryFn: async () => {
      const response = await mediaApi.getMedia(params);
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useMediaItem(id: string) {
  return useQuery({
    queryKey: ['media', id],
    queryFn: async () => {
      const response = await mediaApi.getMediaItem(id);
      return response.data.media;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUploadMedia() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await mediaApi.uploadMedia(formData);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['media'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      queryClient.invalidateQueries({ queryKey: ['recent-media'] });
      // Don't show individual success messages - handled by the upload component
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to upload media';
      toast.error(message);
    },
  });
}

export function useUpdateMedia() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Media> }) => {
      const response = await mediaApi.updateMedia(id, data);
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['media'] });
      queryClient.invalidateQueries({ queryKey: ['media', variables.id] });
      toast.success('Media updated successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to update media';
      toast.error(message);
    },
  });
}

export function useDeleteMedia() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await mediaApi.deleteMedia(id);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['media'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      queryClient.invalidateQueries({ queryKey: ['recent-media'] });
      toast.success('Media deleted successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to delete media';
      toast.error(message);
    },
  });
}

export function useOptimizeMedia() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, options }: { id: string; options?: any }) => {
      const response = await mediaApi.optimizeMedia(id, options || {});
      return response.data;
    },
    onSuccess: (data, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['media'] });
      queryClient.invalidateQueries({ queryKey: ['media', id] });
      toast.success('Media optimized successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to optimize media';
      toast.error(message);
    },
  });
}

export function useBulkDeleteMedia() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (ids: string[]) => {
      const promises = ids.map(id => mediaApi.deleteMedia(id));
      const responses = await Promise.all(promises);
      return responses;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['media'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      queryClient.invalidateQueries({ queryKey: ['recent-media'] });
      toast.success('Media files deleted successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to delete media files';
      toast.error(message);
    },
  });
}
