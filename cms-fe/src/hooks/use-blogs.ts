'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { blogsApi } from '@/lib/api';
import { Blog, QueryParams, CreateBlogForm } from '@/types';
import toast from 'react-hot-toast';

export function useBlogs(params?: QueryParams & { websiteId?: string }) {
  return useQuery({
    queryKey: ['blogs', params],
    queryFn: async () => {
      const response = await blogsApi.getBlogs(params);
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useBlog(id: string) {
  return useQuery({
    queryKey: ['blog', id],
    queryFn: async () => {
      const response = await blogsApi.getBlog(id);
      return response.data.blog;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCreateBlog() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateBlogForm) => {
      const response = await blogsApi.createBlog(data);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Blog created successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to create blog';
      toast.error(message);
    },
  });
}

export function useUpdateBlog() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<CreateBlogForm> }) => {
      const response = await blogsApi.updateBlog(id, data);
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      queryClient.invalidateQueries({ queryKey: ['blog', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Blog updated successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to update blog';
      toast.error(message);
    },
  });
}

export function useDeleteBlog() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await blogsApi.deleteBlog(id);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Blog deleted successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to delete blog';
      toast.error(message);
    },
  });
}

export function usePublishBlog() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await blogsApi.publishBlog(id);
      return response.data;
    },
    onSuccess: (data, id) => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      queryClient.invalidateQueries({ queryKey: ['blog', id] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Blog published successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to publish blog';
      toast.error(message);
    },
  });
}

export function useScheduleBlog() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, scheduledAt }: { id: string; scheduledAt: string }) => {
      const response = await blogsApi.scheduleBlog(id, scheduledAt);
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      queryClient.invalidateQueries({ queryKey: ['blog', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Blog scheduled successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to schedule blog';
      toast.error(message);
    },
  });
}

export function useTrashedBlogs(params?: any) {
  return useQuery({
    queryKey: ['trashed-blogs', params],
    queryFn: () => blogsApi.getTrashedBlogs(params),
  });
}

export function useRestoreBlog() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await blogsApi.restoreBlog(id);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      queryClient.invalidateQueries({ queryKey: ['trashed-blogs'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Blog restored successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to restore blog';
      toast.error(message);
    },
  });
}

export function usePermanentDeleteBlog() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await blogsApi.permanentDeleteBlog(id);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trashed-blogs'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Blog permanently deleted!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to permanently delete blog';
      toast.error(message);
    },
  });
}
