'use client';

import { useQuery } from '@tanstack/react-query';
import { websitesApi, blogsApi, mediaApi, usersApi } from '@/lib/api';
import { DashboardStats } from '@/types';

export function useDashboardStats() {
  return useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async (): Promise<DashboardStats> => {
      const [websiteStats, blogStats, mediaStats, userStats] = await Promise.all([
        websitesApi.getWebsiteStats().catch(() => ({ data: { stats: { total: 0, active: 0, inactive: 0 } } })),
        blogsApi.getBlogStats().catch(() => ({ data: { stats: { total: 0, published: 0, draft: 0, scheduled: 0, totalViews: 0 } } })),
        mediaApi.getMediaStats().catch(() => ({ data: { stats: { total: 0, totalSize: 0, totalSizeMB: 0 } } })),
        usersApi.getUserStats().catch(() => ({ data: { stats: { total: 0, active: 0, inactive: 0 } } })),
      ]);

      return {
        websites: websiteStats.data.stats,
        blogs: blogStats.data.stats,
        media: mediaStats.data.stats,
        users: userStats.data.stats,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useRecentBlogs() {
  return useQuery({
    queryKey: ['recent-blogs'],
    queryFn: async () => {
      const response = await blogsApi.getBlogs({ 
        limit: 5, 
        sortBy: 'createdAt', 
        sortOrder: 'desc' 
      });
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useRecentMedia() {
  return useQuery({
    queryKey: ['recent-media'],
    queryFn: async () => {
      const response = await mediaApi.getRecentMedia(6);
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useMyWebsites() {
  return useQuery({
    queryKey: ['my-websites'],
    queryFn: async () => {
      console.log('🔍 Fetching websites...');
      const response = await websitesApi.getMyWebsites();
      console.log('📡 Raw API response:', response);
      console.log('📊 Response data:', response.data);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    retryDelay: 1000,
  });
}
