'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { websitesApi } from '@/lib/api';
import { Website, QueryParams, CreateWebsiteForm } from '@/types';
import toast from 'react-hot-toast';

export function useWebsites(params?: QueryParams) {
  return useQuery({
    queryKey: ['websites', params],
    queryFn: async () => {
      const response = await websitesApi.getWebsites(params);
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useWebsite(id: string) {
  return useQuery({
    queryKey: ['website', id],
    queryFn: async () => {
      const response = await websitesApi.getWebsite(id);
      return response.data.website;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCreateWebsite() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateWebsiteForm) => {
      const response = await websitesApi.createWebsite(data);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['websites'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Website created successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to create website';
      toast.error(message);
    },
  });
}

export function useUpdateWebsite() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<CreateWebsiteForm> }) => {
      const response = await websitesApi.updateWebsite(id, data);
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['websites'] });
      queryClient.invalidateQueries({ queryKey: ['website', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Website updated successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to update website';
      toast.error(message);
    },
  });
}

export function useDeleteWebsite() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await websitesApi.deleteWebsite(id);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['websites'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      queryClient.invalidateQueries({ queryKey: ['recent-blogs'] });
      toast.success('Website deleted successfully! All associated blog posts have been removed.');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to delete website';
      toast.error(message);
    },
  });
}

export function useRegenerateApiKey() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await websitesApi.regenerateApiKey(id);
      return response.data;
    },
    onSuccess: (data, id) => {
      queryClient.invalidateQueries({ queryKey: ['website', id] });
      queryClient.invalidateQueries({ queryKey: ['websites'] });
      toast.success('API key regenerated successfully!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to regenerate API key';
      toast.error(message);
    },
  });
}
