import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { IntegrationDocs } from '@/components/website/integration-docs';
import toast from 'react-hot-toast';

// Mock toast
jest.mock('react-hot-toast', () => ({
  success: jest.fn(),
  error: jest.fn(),
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(),
  },
});

const mockWebsite = {
  _id: 'website123',
  name: 'Test Website',
  domain: 'test.com',
  apiKey: 'test-api-key-123',
};

describe('IntegrationDocs Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders integration documentation', () => {
    render(<IntegrationDocs website={mockWebsite} />);
    
    expect(screen.getByText('Integration Documentation')).toBeInTheDocument();
    expect(screen.getByText('Quick Start Guide')).toBeInTheDocument();
    expect(screen.getByText('Code Examples')).toBeInTheDocument();
    expect(screen.getByText('API Endpoints')).toBeInTheDocument();
  });

  it('displays website information correctly', () => {
    render(<IntegrationDocs website={mockWebsite} />);
    
    expect(screen.getByText('website123')).toBeInTheDocument();
    expect(screen.getByText('test-api-key-123')).toBeInTheDocument();
    expect(screen.getByText(process.env.NEXT_PUBLIC_API_URL || 'https://creasoft-cms-panel-3f918af5b53d.herokuapp.com')).toBeInTheDocument();
  });

  it('copies website ID to clipboard', async () => {
    const mockWriteText = navigator.clipboard.writeText as jest.Mock;
    mockWriteText.mockResolvedValue(undefined);

    render(<IntegrationDocs website={mockWebsite} />);
    
    const copyButtons = screen.getAllByRole('button');
    const websiteIdCopyButton = copyButtons.find(button => 
      button.closest('div')?.textContent?.includes('website123')
    );
    
    if (websiteIdCopyButton) {
      fireEvent.click(websiteIdCopyButton);
      
      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalledWith('website123');
        expect(toast.success).toHaveBeenCalledWith('Website ID copied to clipboard!');
      });
    }
  });

  it('copies API key to clipboard', async () => {
    const mockWriteText = navigator.clipboard.writeText as jest.Mock;
    mockWriteText.mockResolvedValue(undefined);

    render(<IntegrationDocs website={mockWebsite} />);
    
    const copyButtons = screen.getAllByRole('button');
    const apiKeyCopyButton = copyButtons.find(button => 
      button.closest('div')?.textContent?.includes('test-api-key-123')
    );
    
    if (apiKeyCopyButton) {
      fireEvent.click(apiKeyCopyButton);
      
      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalledWith('test-api-key-123');
        expect(toast.success).toHaveBeenCalledWith('API Key copied to clipboard!');
      });
    }
  });

  it('handles clipboard copy errors', async () => {
    const mockWriteText = navigator.clipboard.writeText as jest.Mock;
    mockWriteText.mockRejectedValue(new Error('Clipboard error'));

    render(<IntegrationDocs website={mockWebsite} />);
    
    const copyButtons = screen.getAllByRole('button');
    const firstCopyButton = copyButtons[0];
    
    fireEvent.click(firstCopyButton);
    
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to copy to clipboard');
    });
  });

  it('displays code examples for different frameworks', () => {
    render(<IntegrationDocs website={mockWebsite} />);
    
    // Check for framework tabs
    expect(screen.getByText('JavaScript')).toBeInTheDocument();
    expect(screen.getByText('React')).toBeInTheDocument();
    expect(screen.getByText('Next.js')).toBeInTheDocument();
  });

  it('switches between fetch blogs and fetch single blog examples', () => {
    render(<IntegrationDocs website={mockWebsite} />);
    
    // Check default tab
    expect(screen.getByText('Fetch All Blogs')).toBeInTheDocument();
    expect(screen.getByText('Fetch Single Blog')).toBeInTheDocument();
    
    // Click on fetch single blog tab
    fireEvent.click(screen.getByText('Fetch Single Blog'));
    
    // Should show single blog code examples
    expect(screen.getByText(/fetch a single blog by slug/i)).toBeInTheDocument();
  });

  it('displays API endpoints information', () => {
    render(<IntegrationDocs website={mockWebsite} />);
    
    expect(screen.getByText('/api/v1/public/blogs')).toBeInTheDocument();
    expect(screen.getByText('/api/v1/public/blogs/slug/:slug')).toBeInTheDocument();
    expect(screen.getByText('/api/v1/public/blogs/:id')).toBeInTheDocument();
  });

  it('shows response format example', () => {
    render(<IntegrationDocs website={mockWebsite} />);
    
    expect(screen.getByText('Response Format')).toBeInTheDocument();
    expect(screen.getByText('Blog List Response')).toBeInTheDocument();
  });

  it('includes website ID in code examples', () => {
    render(<IntegrationDocs website={mockWebsite} />);
    
    // Check if website ID is included in the code examples
    const codeBlocks = screen.getAllByRole('code');
    const hasWebsiteId = codeBlocks.some(block => 
      block.textContent?.includes('website123')
    );
    
    expect(hasWebsiteId).toBe(true);
  });

  it('shows quick start guide steps', () => {
    render(<IntegrationDocs website={mockWebsite} />);
    
    expect(screen.getByText('Choose your integration method')).toBeInTheDocument();
    expect(screen.getByText('Fetch your blogs')).toBeInTheDocument();
    expect(screen.getByText('Render the content')).toBeInTheDocument();
  });

  it('displays HTTP methods for API endpoints', () => {
    render(<IntegrationDocs website={mockWebsite} />);
    
    const getMethods = screen.getAllByText('GET');
    expect(getMethods.length).toBeGreaterThan(0);
  });

  it('shows query parameters for endpoints', () => {
    render(<IntegrationDocs website={mockWebsite} />);
    
    expect(screen.getByText(/websiteId.*required/)).toBeInTheDocument();
    expect(screen.getByText(/page.*optional/)).toBeInTheDocument();
    expect(screen.getByText(/limit.*optional/)).toBeInTheDocument();
  });
});
