import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import NewBlogPage from '@/app/blogs/new/page';
import { useMyWebsites } from '@/hooks/use-dashboard';
import { useCreateBlog } from '@/hooks/use-blogs';

// Mock the hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: () => ({
    get: jest.fn(() => null),
  }),
}));

jest.mock('@/hooks/use-dashboard', () => ({
  useMyWebsites: jest.fn(),
}));

jest.mock('@/hooks/use-blogs', () => ({
  useCreateBlog: jest.fn(),
}));

jest.mock('@/lib/api', () => ({
  mediaApi: {
    uploadMedia: jest.fn(),
  },
}));

const mockRouter = {
  push: jest.fn(),
  back: jest.fn(),
};

const mockWebsites = {
  websites: [
    {
      _id: 'website1',
      name: 'Test Website 1',
      domain: 'test1.com',
    },
    {
      _id: 'website2',
      name: 'Test Website 2',
      domain: 'test2.com',
    },
  ],
};

const mockCreateBlog = {
  mutate: jest.fn(),
  isPending: false,
};

describe('Blog Creation Page', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useMyWebsites as jest.Mock).mockReturnValue({
      data: mockWebsites,
      isLoading: false,
      error: null,
    });
    (useCreateBlog as jest.Mock).mockReturnValue(mockCreateBlog);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <NewBlogPage />
      </QueryClientProvider>
    );
  };

  it('renders the blog creation form', () => {
    renderComponent();
    
    expect(screen.getByText('Create New Blog')).toBeInTheDocument();
    expect(screen.getByLabelText(/title/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/slug/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/excerpt/i)).toBeInTheDocument();
    expect(screen.getByText('Select a website')).toBeInTheDocument();
  });

  it('displays website options in dropdown', () => {
    renderComponent();
    
    const websiteSelect = screen.getByRole('combobox');
    expect(websiteSelect).toBeInTheDocument();
    
    // Check if websites are loaded
    expect(screen.getByText('Test Website 1')).toBeInTheDocument();
    expect(screen.getByText('Test Website 2')).toBeInTheDocument();
  });

  it('shows loading state when websites are loading', () => {
    (useMyWebsites as jest.Mock).mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    });

    renderComponent();
    
    expect(screen.getByText('Loading websites...')).toBeInTheDocument();
  });

  it('shows error state when websites fail to load', () => {
    (useMyWebsites as jest.Mock).mockReturnValue({
      data: null,
      isLoading: false,
      error: new Error('Failed to load'),
    });

    renderComponent();
    
    expect(screen.getByText('Failed to load websites. Please refresh the page.')).toBeInTheDocument();
  });

  it('shows message when no websites are available', () => {
    (useMyWebsites as jest.Mock).mockReturnValue({
      data: { websites: [] },
      isLoading: false,
      error: null,
    });

    renderComponent();
    
    expect(screen.getByText('No websites found. Please create a website first.')).toBeInTheDocument();
  });

  it('auto-generates slug from title', async () => {
    renderComponent();
    
    const titleInput = screen.getByLabelText(/title/i);
    const slugInput = screen.getByLabelText(/slug/i);
    
    fireEvent.change(titleInput, { target: { value: 'My Test Blog Post' } });
    
    await waitFor(() => {
      expect(slugInput).toHaveValue('my-test-blog-post');
    });
  });

  it('disables auto-slug generation when user manually edits slug', async () => {
    renderComponent();
    
    const titleInput = screen.getByLabelText(/title/i);
    const slugInput = screen.getByLabelText(/slug/i);
    
    // First, auto-generate slug
    fireEvent.change(titleInput, { target: { value: 'My Test Blog Post' } });
    
    await waitFor(() => {
      expect(slugInput).toHaveValue('my-test-blog-post');
    });
    
    // Then manually edit slug
    fireEvent.change(slugInput, { target: { value: 'custom-slug' } });
    
    // Change title again - slug should not auto-update
    fireEvent.change(titleInput, { target: { value: 'Updated Title' } });
    
    await waitFor(() => {
      expect(slugInput).toHaveValue('custom-slug');
    });
  });

  it('validates required fields', async () => {
    renderComponent();
    
    const publishButton = screen.getByText('Publish');
    fireEvent.click(publishButton);
    
    await waitFor(() => {
      expect(screen.getByText('Title must be at least 2 characters')).toBeInTheDocument();
      expect(screen.getByText('Please select a website')).toBeInTheDocument();
    });
  });

  it('submits form with correct data', async () => {
    renderComponent();
    
    // Fill out the form
    fireEvent.change(screen.getByLabelText(/title/i), {
      target: { value: 'Test Blog Post' },
    });
    
    fireEvent.change(screen.getByLabelText(/excerpt/i), {
      target: { value: 'This is a test blog post excerpt' },
    });
    
    fireEvent.change(screen.getByRole('combobox'), {
      target: { value: 'website1' },
    });
    
    // Submit the form
    const publishButton = screen.getByText('Publish');
    fireEvent.click(publishButton);
    
    await waitFor(() => {
      expect(mockCreateBlog.mutate).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Blog Post',
          slug: 'test-blog-post',
          excerpt: 'This is a test blog post excerpt',
          websiteId: 'website1',
          status: 'published',
        })
      );
    });
  });

  it('handles categories and tags input', () => {
    renderComponent();
    
    const categoriesInput = screen.getByPlaceholderText(/categories/i);
    const tagsInput = screen.getByPlaceholderText(/tags/i);
    
    fireEvent.change(categoriesInput, {
      target: { value: 'tech, programming, web development' },
    });
    
    fireEvent.change(tagsInput, {
      target: { value: 'react, nextjs, typescript' },
    });
    
    expect(categoriesInput).toHaveValue('tech, programming, web development');
    expect(tagsInput).toHaveValue('react, nextjs, typescript');
  });

  it('shows rich text editor', () => {
    renderComponent();
    
    // Check for rich text editor toolbar buttons
    expect(screen.getByRole('button', { name: /bold/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /italic/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /heading/i })).toBeInTheDocument();
  });
});
