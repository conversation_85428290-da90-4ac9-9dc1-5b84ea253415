/**
 * Generate an excerpt from HTML content
 * @param content - HTML content string
 * @param maxLength - Maximum length of excerpt (default: 160)
 * @returns Clean text excerpt
 */
export function generateExcerpt(content: string, maxLength: number = 160): string {
  if (!content || content.trim() === '') {
    return '';
  }

  // Remove HTML tags
  const textContent = content.replace(/<[^>]*>/g, '');
  
  // Decode HTML entities
  const decodedContent = textContent
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'");
  
  // Clean up whitespace
  const cleanContent = decodedContent
    .replace(/\s+/g, ' ')
    .trim();
  
  // If content is shorter than max length, return as is
  if (cleanContent.length <= maxLength) {
    return cleanContent;
  }
  
  // Find the last complete word within the limit
  const truncated = cleanContent.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');
  
  // If we found a space, cut at the last complete word
  if (lastSpaceIndex > 0) {
    return truncated.substring(0, lastSpaceIndex) + '...';
  }
  
  // Otherwise, just truncate and add ellipsis
  return truncated + '...';
}

/**
 * Check if excerpt needs to be updated based on content changes
 * @param currentExcerpt - Current excerpt
 * @param content - Current content
 * @param maxLength - Maximum length for auto-generated excerpt
 * @returns Whether excerpt should be auto-updated
 */
export function shouldUpdateExcerpt(
  currentExcerpt: string, 
  content: string, 
  maxLength: number = 160
): boolean {
  if (!currentExcerpt || currentExcerpt.trim() === '') {
    return true;
  }
  
  // If current excerpt looks auto-generated (ends with ...), update it
  if (currentExcerpt.endsWith('...')) {
    const autoGenerated = generateExcerpt(content, maxLength);
    return currentExcerpt !== autoGenerated;
  }
  
  return false;
}
