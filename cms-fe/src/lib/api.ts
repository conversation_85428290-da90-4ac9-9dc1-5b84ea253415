import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'https://creasoft-cms-panel-3f918af5b53d.herokuapp.com',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Clear auth token and redirect to login
      Cookies.remove('auth-token');
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login';
      }
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.response?.data?.message) {
      toast.error(error.response.data.message);
    } else if (error.message) {
      toast.error(error.message);
    }
    
    return Promise.reject(error);
  }
);

// API methods
export const apiClient = {
  // Generic methods
  get: <T = any>(url: string, config?: AxiosRequestConfig) =>
    api.get<T>(url, config),
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    api.post<T>(url, data, config),
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    api.put<T>(url, data, config),
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    api.patch<T>(url, data, config),
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig) =>
    api.delete<T>(url, config),

  // File upload
  upload: <T = any>(url: string, formData: FormData, config?: AxiosRequestConfig) =>
    api.post<T>(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    }),
};

// Auth API
export const authApi = {
  login: (data: { email: string; password: string }) =>
    apiClient.post('/api/v1/auth/login', data),
  
  register: (data: { email: string; password: string; firstName: string; lastName: string }) =>
    apiClient.post('/api/v1/auth/register', data),
  
  getProfile: () =>
    apiClient.get('/api/v1/auth/profile'),

  updateProfile: (data: { firstName: string; lastName: string; email: string }) =>
    apiClient.patch('/api/v1/auth/profile', data),

  changePassword: (data: { currentPassword: string; newPassword: string }) =>
    apiClient.post('/api/v1/auth/change-password', data),
  
  forgotPassword: (data: { email: string }) =>
    apiClient.post('/api/v1/auth/forgot-password', data),
  
  resetPassword: (data: { token: string; newPassword: string }) =>
    apiClient.post('/api/v1/auth/reset-password', data),
};

// Users API
export const usersApi = {
  getUsers: (params?: any) =>
    apiClient.get('/api/v1/users', { params }),
  
  getUser: (id: string) =>
    apiClient.get(`/api/v1/users/${id}`),
  
  createUser: (data: any) =>
    apiClient.post('/api/v1/users', data),
  
  updateUser: (id: string, data: any) =>
    apiClient.patch(`/api/v1/users/${id}`, data),
  
  deleteUser: (id: string) =>
    apiClient.delete(`/api/v1/users/${id}`),
  
  getUserStats: () =>
    apiClient.get('/api/v1/users/stats'),

  toggleUserStatus: (id: string) =>
    apiClient.patch(`/api/v1/users/${id}/toggle-status`),

  resetUserPassword: (id: string) =>
    apiClient.post(`/api/v1/users/${id}/reset-password`),
};

// Websites API
export const websitesApi = {
  getWebsites: (params?: any) =>
    apiClient.get('/api/v1/websites', { params }),
  
  getWebsite: (id: string) =>
    apiClient.get(`/api/v1/websites/${id}`),
  
  createWebsite: (data: any) =>
    apiClient.post('/api/v1/websites', data),
  
  updateWebsite: (id: string, data: any) =>
    apiClient.patch(`/api/v1/websites/${id}`, data),
  
  deleteWebsite: (id: string) =>
    apiClient.delete(`/api/v1/websites/${id}`),
  
  regenerateApiKey: (id: string) =>
    apiClient.post(`/api/v1/websites/${id}/regenerate-api-key`),
  
  getWebsiteStats: () =>
    apiClient.get('/api/v1/websites/stats'),
  
  getMyWebsites: () =>
    apiClient.get('/api/v1/websites/my-websites'),
};

// Blogs API
export const blogsApi = {
  getBlogs: (params?: any) =>
    apiClient.get('/api/v1/blogs', { params }),
  
  getBlog: (id: string) =>
    apiClient.get(`/api/v1/blogs/${id}`),
  
  createBlog: (data: any) =>
    apiClient.post('/api/v1/blogs', data),
  
  updateBlog: (id: string, data: any) =>
    apiClient.patch(`/api/v1/blogs/${id}`, data),
  
  deleteBlog: (id: string) =>
    apiClient.delete(`/api/v1/blogs/${id}`),
  
  publishBlog: (id: string) =>
    apiClient.post(`/api/v1/blogs/${id}/publish`),
  
  scheduleBlog: (id: string, scheduledAt: string) =>
    apiClient.post(`/api/v1/blogs/${id}/schedule`, { scheduledAt }),
  
  getBlogStats: (websiteId?: string) =>
    apiClient.get('/api/v1/blogs/stats', { params: { websiteId } }),

  getTrashedBlogs: (params?: any) =>
    apiClient.get('/api/v1/blogs/trash', { params }),

  restoreBlog: (id: string) =>
    apiClient.post(`/api/v1/blogs/${id}/restore`),

  permanentDeleteBlog: (id: string) =>
    apiClient.delete(`/api/v1/blogs/${id}/permanent`),
};

// Media API
export const mediaApi = {
  getMedia: (params?: any) =>
    apiClient.get('/api/v1/media', { params }),
  
  getMediaItem: (id: string) =>
    apiClient.get(`/api/v1/media/${id}`),
  
  uploadMedia: (formData: FormData) =>
    apiClient.upload('/api/v1/media/upload', formData),
  
  updateMedia: (id: string, data: any) =>
    apiClient.patch(`/api/v1/media/${id}`, data),
  
  deleteMedia: (id: string) =>
    apiClient.delete(`/api/v1/media/${id}`),
  
  getMediaStats: () =>
    apiClient.get('/api/v1/media/stats'),
  
  getRecentMedia: (limit?: number) =>
    apiClient.get('/api/v1/media/my-recent', { params: { limit } }),
  
  optimizeMedia: (id: string, options: any) =>
    apiClient.post(`/api/v1/media/${id}/optimize`, options),
};

// Categories API
export const categoriesApi = {
  getCategories: (params?: any) =>
    apiClient.get('/api/v1/categories', { params }),

  getCategory: (id: string) =>
    apiClient.get(`/api/v1/categories/${id}`),

  createCategory: (data: any) =>
    apiClient.post('/api/v1/categories', data),

  updateCategory: (id: string, data: any) =>
    apiClient.patch(`/api/v1/categories/${id}`, data),

  deleteCategory: (id: string) =>
    apiClient.delete(`/api/v1/categories/${id}`),

  getCategoryStats: (websiteId?: string) =>
    apiClient.get('/api/v1/categories/stats', { params: { websiteId } }),
};

// Notifications API
export const notificationsApi = {
  getNotifications: () =>
    apiClient.get('/api/v1/notifications'),

  markAsRead: (id: string) =>
    apiClient.patch(`/api/v1/notifications/${id}/read`),

  markAllAsRead: () =>
    apiClient.patch('/api/v1/notifications/read-all'),

  deleteNotification: (id: string) =>
    apiClient.delete(`/api/v1/notifications/${id}`),

  createNotification: (data: any) =>
    apiClient.post('/api/v1/notifications', data),
};

export default api;
