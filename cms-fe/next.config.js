/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'res.cloudinary.com',
      'cloudinary.com',
      'localhost',
      'creasoft-cms-panel-3f918af5b53d.herokuapp.com',
    ],
  },
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'https://creasoft-cms-panel-3f918af5b53d.herokuapp.com'}/api/v1/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;
