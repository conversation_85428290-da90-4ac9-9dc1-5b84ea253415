import { ConfigModule } from '@nestjs/config';

export class TestConfigUtil {
  static getTestConfig() {
    return ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env.test',
      load: [
        () => ({
          // Database configuration
          database: {
            uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/test',
          },
          
          // JWT configuration for testing
          jwt: {
            secret: process.env.JWT_SECRET || 'test-jwt-secret-key',
            expiresIn: process.env.JWT_EXPIRES_IN || '1h',
          },
          
          // API configuration
          api: {
            port: process.env.PORT || 3001,
            prefix: process.env.API_PREFIX || 'api',
          },
          
          // Test-specific settings
          test: {
            timeout: 30000,
            maxWorkers: 1,
            verbose: process.env.TEST_VERBOSE === 'true',
          },
          
          // Security settings for testing
          security: {
            bcryptRounds: 4, // Lower rounds for faster testing
            apiKeyPrefix: 'creasoft_',
            apiKeyLength: 32,
          },
        }),
      ],
    });
  }

  static getTestEnvironmentVariables(): Record<string, string> {
    return {
      NODE_ENV: 'test',
      JWT_SECRET: 'test-jwt-secret-key',
      JWT_EXPIRES_IN: '1h',
      API_PREFIX: 'api',
      TEST_VERBOSE: 'false',
      BCRYPT_ROUNDS: '4',
    };
  }

  static setupTestEnvironment(): void {
    const testEnvVars = this.getTestEnvironmentVariables();
    Object.entries(testEnvVars).forEach(([key, value]) => {
      if (!process.env[key]) {
        process.env[key] = value;
      }
    });
  }
}