import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import * as request from 'supertest';
import { TestDatabaseUtil } from './test-database.util';

export class TestHelper {
  private app: INestApplication;
  private moduleRef: TestingModule;

  async createTestingApp(moduleClass: any, additionalProviders: any[] = []): Promise<INestApplication> {
    const mongoUri = await TestDatabaseUtil.createMemoryDatabase();

    const moduleBuilder = Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(mongoUri),
        moduleClass,
      ],
      providers: [...additionalProviders],
    });

    this.moduleRef = await moduleBuilder.compile();
    this.app = this.moduleRef.createNestApplication();
    
    await this.app.init();
    return this.app;
  }

  async closeTestingApp(): Promise<void> {
    if (this.app) {
      await this.app.close();
    }
    if (this.moduleRef) {
      await this.moduleRef.close();
    }
    await TestDatabaseUtil.closeDatabase();
  }

  async clearDatabase(): Promise<void> {
    await TestDatabaseUtil.clearDatabase();
  }

  getApp(): INestApplication {
    return this.app;
  }

  getRequest(): request.SuperTest<request.Test> {
    return request(this.app.getHttpServer());
  }

  async waitForDatabase(): Promise<void> {
    let retries = 10;
    while (retries > 0 && !TestDatabaseUtil.isConnected()) {
      await new Promise(resolve => setTimeout(resolve, 100));
      retries--;
    }
    
    if (!TestDatabaseUtil.isConnected()) {
      throw new Error('Database connection timeout');
    }
  }
}