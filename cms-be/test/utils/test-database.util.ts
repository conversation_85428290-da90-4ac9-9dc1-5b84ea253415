import { MongoMemoryServer } from 'mongodb-memory-server';
import { Connection } from 'mongoose';
import * as mongoose from 'mongoose';

export class TestDatabaseUtil {
  private static mongod: MongoMemoryServer;

  static async createMemoryDatabase(): Promise<string> {
    this.mongod = await MongoMemoryServer.create();
    return this.mongod.getUri();
  }

  static async closeDatabase(): Promise<void> {
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.dropDatabase();
      await mongoose.connection.close();
    }
    
    if (this.mongod) {
      await this.mongod.stop();
    }
  }

  static async clearDatabase(): Promise<void> {
    if (mongoose.connection.readyState === 1) {
      const collections = mongoose.connection.collections;
      for (const key in collections) {
        const collection = collections[key];
        await collection.deleteMany({});
      }
    }
  }

  static async getConnection(): Promise<Connection> {
    return mongoose.connection;
  }

  static isConnected(): boolean {
    return mongoose.connection.readyState === 1;
  }
}