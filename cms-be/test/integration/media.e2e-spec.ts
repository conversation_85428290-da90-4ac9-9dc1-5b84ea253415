import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { MongooseModule } from '@nestjs/mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import * as path from 'path';

describe('MediaController (e2e)', () => {
  let app: INestApplication;
  let mongod: MongoMemoryServer;
  let authToken: string;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(uri),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Create a test user and get auth token
    const registerResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/register')
      .send({
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'password123',
      });

    const loginResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
      });

    authToken = loginResponse.body.access_token;
  });

  afterAll(async () => {
    await app.close();
    await mongod.stop();
  });

  describe('/api/v1/media (GET)', () => {
    it('should return media list with empty type parameter', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/media?type=')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should return media list with search parameter', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/media?search=test')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should handle pagination parameters', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/media?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('page', 1);
      expect(response.body).toHaveProperty('limit', 10);
      expect(response.body).toHaveProperty('totalPages');
      expect(response.body).toHaveProperty('hasNext');
      expect(response.body).toHaveProperty('hasPrev');
    });
  });

  describe('/api/v1/media/stats (GET)', () => {
    it('should return media statistics', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/media/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('stats');
      expect(response.body.stats).toHaveProperty('totalFiles');
      expect(response.body.stats).toHaveProperty('totalSize');
      expect(response.body.stats).toHaveProperty('fileTypes');
    });
  });

  describe('/api/v1/media/my-recent (GET)', () => {
    it('should return recent media files', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/media/my-recent?limit=5')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('media');
      expect(Array.isArray(response.body.media)).toBe(true);
    });
  });

  describe('/api/v1/media/:id (GET)', () => {
    it('should return 400 for invalid ObjectId', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/media/invalid-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.message).toContain('Invalid media ID format');
    });

    it('should return 404 for non-existent media', async () => {
      const validObjectId = '507f1f77bcf86cd799439011';
      const response = await request(app.getHttpServer())
        .get(`/api/v1/media/${validObjectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.message).toContain('Media not found');
    });
  });
});
