import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { MongooseModule } from '@nestjs/mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';

describe('BlogsController (e2e)', () => {
  let app: INestApplication;
  let mongod: MongoMemoryServer;
  let authToken: string;
  let websiteId: string;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(uri),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Create a test user and get auth token
    const registerResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/register')
      .send({
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'password123',
      });

    const loginResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
      });

    authToken = loginResponse.body.access_token;

    // Create a test website
    const websiteResponse = await request(app.getHttpServer())
      .post('/api/v1/websites')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        name: 'Test Website',
        domain: 'test.example.com',
        description: 'Test website for e2e tests',
      });

    websiteId = websiteResponse.body.website._id;
  });

  afterAll(async () => {
    await app.close();
    await mongod.stop();
  });

  describe('/api/v1/blogs/trash (GET)', () => {
    it('should return trashed blogs without ObjectId cast error', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/blogs/trash')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('message');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('/api/v1/blogs (POST)', () => {
    it('should create a blog with auto-generated excerpt', async () => {
      const blogData = {
        title: 'Test Blog',
        content: '<p>This is a test blog content with more than 160 characters to test the auto-excerpt generation feature. It should automatically create an excerpt from this content.</p>',
        websiteId: websiteId,
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${authToken}`)
        .send(blogData)
        .expect(201);

      expect(response.body.blog).toHaveProperty('excerpt');
      expect(response.body.blog.excerpt).toBeTruthy();
      expect(response.body.blog.excerpt.length).toBeGreaterThan(0);
      expect(response.body.blog.excerpt).toContain('This is a test blog content');
    });

    it('should use provided excerpt when given', async () => {
      const blogData = {
        title: 'Test Blog with Custom Excerpt',
        content: '<p>This is the full content of the blog post.</p>',
        excerpt: 'This is a custom excerpt',
        websiteId: websiteId,
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${authToken}`)
        .send(blogData)
        .expect(201);

      expect(response.body.blog.excerpt).toBe('This is a custom excerpt');
    });
  });

  describe('/api/v1/blogs/:id (GET)', () => {
    it('should return 400 for invalid ObjectId', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/blogs/invalid-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.message).toContain('Invalid blog ID format');
    });
  });
});
