import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { MongooseModule } from '@nestjs/mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';

describe('CategoriesController (e2e)', () => {
  let app: INestApplication;
  let mongod: MongoMemoryServer;
  let authToken: string;
  let websiteId: string;
  let categoryId: string;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(uri),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Create a test user and get auth token
    const registerResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/register')
      .send({
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'password123',
      });

    const loginResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
      });

    authToken = loginResponse.body.access_token;

    // Create a test website
    const websiteResponse = await request(app.getHttpServer())
      .post('/api/v1/websites')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        name: 'Test Website',
        domain: 'test.example.com',
        description: 'Test website for e2e tests',
      });

    websiteId = websiteResponse.body.website._id;
  });

  afterAll(async () => {
    await app.close();
    await mongod.stop();
  });

  describe('/api/v1/categories (POST)', () => {
    it('should create a new category', async () => {
      const categoryData = {
        name: 'Technology',
        description: 'Posts about technology and innovation',
        color: '#3B82F6',
        websiteId: websiteId,
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/categories')
        .set('Authorization', `Bearer ${authToken}`)
        .send(categoryData)
        .expect(201);

      expect(response.body.category).toHaveProperty('_id');
      expect(response.body.category.name).toBe('Technology');
      expect(response.body.category.slug).toBe('technology');
      expect(response.body.category.color).toBe('#3B82F6');
      expect(response.body.category.postCount).toBe(0);

      categoryId = response.body.category._id;
    });

    it('should auto-generate slug from name', async () => {
      const categoryData = {
        name: 'Web Development & Design',
        websiteId: websiteId,
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/categories')
        .set('Authorization', `Bearer ${authToken}`)
        .send(categoryData)
        .expect(201);

      expect(response.body.category.slug).toBe('web-development-design');
    });
  });

  describe('/api/v1/categories (GET)', () => {
    it('should return categories list', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/categories')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.total).toBeGreaterThan(0);
    });

    it('should filter categories by search', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/categories?search=technology')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0].name.toLowerCase()).toContain('technology');
    });
  });

  describe('/api/v1/categories/:id (GET)', () => {
    it('should return a specific category', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.category._id).toBe(categoryId);
      expect(response.body.category.name).toBe('Technology');
    });

    it('should return 400 for invalid ObjectId', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/categories/invalid-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.message).toContain('Invalid category ID format');
    });
  });

  describe('/api/v1/categories/:id (PATCH)', () => {
    it('should update a category', async () => {
      const updateData = {
        name: 'Updated Technology',
        description: 'Updated description',
        color: '#FF5722',
      };

      const response = await request(app.getHttpServer())
        .patch(`/api/v1/categories/${categoryId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.category.name).toBe('Updated Technology');
      expect(response.body.category.description).toBe('Updated description');
      expect(response.body.category.color).toBe('#FF5722');
    });
  });

  describe('/api/v1/categories/stats (GET)', () => {
    it('should return category statistics', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/categories/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.stats).toHaveProperty('totalCategories');
      expect(response.body.stats).toHaveProperty('categoriesWithPosts');
      expect(response.body.stats).toHaveProperty('categoriesWithoutPosts');
      expect(response.body.stats).toHaveProperty('topCategories');
    });
  });
});
