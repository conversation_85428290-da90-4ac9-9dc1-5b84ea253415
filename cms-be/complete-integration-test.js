const axios = require('axios');

const API_BASE_URL = 'http://localhost:8000';

class IntegrationTester {
  constructor() {
    this.testResults = [];
    this.testUser = null;
    this.testUserToken = null;
    this.testDomain = null;
    this.testBlog = null;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`${emoji} [${timestamp}] ${message}`);
  }

  async test(name, testFn) {
    try {
      this.log(`Starting test: ${name}`, 'info');
      const startTime = Date.now();
      await testFn();
      const duration = Date.now() - startTime;
      this.testResults.push({ name, status: 'PASSED', duration });
      this.log(`Test passed: ${name} (${duration}ms)`, 'success');
    } catch (error) {
      this.testResults.push({ name, status: 'FAILED', error: error.message });
      this.log(`Test failed: ${name} - ${error.message}`, 'error');
      throw error;
    }
  }

  async runCompleteIntegrationTest() {
    this.log('🚀 Starting Complete Integration Flow Test', 'info');
    this.log('=' .repeat(60), 'info');

    try {
      // Test 1: User Registration
      await this.test('User Registration', async () => {
        const userData = {
          email: `test-${Date.now()}@example.com`,
          password: 'password123',
          firstName: 'Integration',
          lastName: 'Test',
        };

        const response = await axios.post(`${API_BASE_URL}/api/v1/auth/register`, userData);
        
        if (response.status !== 201) {
          throw new Error(`Expected status 201, got ${response.status}`);
        }

        this.testUser = response.data.user;
        this.testUserToken = response.data.token;

        if (!this.testUser || !this.testUserToken) {
          throw new Error('User or token not returned');
        }

        this.log(`User registered: ${this.testUser.email}`, 'info');
      });

      // Test 2: Domain/Website Creation
      await this.test('Domain/Website Creation', async () => {
        const domainData = {
          name: 'Integration Test Website',
          domain: `test-${Date.now()}.com`,
          description: 'A test website for complete integration testing',
          theme: 'minimal',
        };

        const response = await axios.post(`${API_BASE_URL}/api/v1/websites`, domainData, {
          headers: { Authorization: `Bearer ${this.testUserToken}` }
        });

        if (response.status !== 201) {
          throw new Error(`Expected status 201, got ${response.status}`);
        }

        this.testDomain = response.data.website;

        if (!this.testDomain.apiKey || !this.testDomain.apiKey.startsWith('creasoft_')) {
          throw new Error('API key not generated correctly');
        }

        this.log(`Domain created: ${this.testDomain.domain}`, 'info');
        this.log(`API Key: ${this.testDomain.apiKey}`, 'info');
      });

      // Test 3: Blog Creation
      await this.test('Blog Creation', async () => {
        const blogData = {
          title: 'Complete Integration Test Blog',
          slug: `integration-test-${Date.now()}`,
          content: '<h1>Integration Test</h1><p>This is a comprehensive test blog post for integration testing.</p>',
          excerpt: 'A test blog post for complete integration testing.',
          websiteId: this.testDomain._id,
          categories: ['Integration', 'Testing'],
          tags: ['integration', 'test', 'complete'],
          isFeatured: true,
          allowComments: true,
        };

        const response = await axios.post(`${API_BASE_URL}/api/v1/blogs`, blogData, {
          headers: { Authorization: `Bearer ${this.testUserToken}` }
        });

        if (response.status !== 201) {
          throw new Error(`Expected status 201, got ${response.status}`);
        }

        this.testBlog = response.data.blog;

        if (this.testBlog.status !== 'draft') {
          throw new Error('Blog should be in draft status initially');
        }

        this.log(`Blog created: ${this.testBlog.title}`, 'info');
      });

      // Test 4: Blog Publishing
      await this.test('Blog Publishing', async () => {
        const response = await axios.post(`${API_BASE_URL}/api/v1/blogs/${this.testBlog._id}/publish`, {}, {
          headers: { Authorization: `Bearer ${this.testUserToken}` }
        });

        if (response.status !== 200 && response.status !== 201) {
          throw new Error(`Expected status 200 or 201, got ${response.status}`);
        }

        const publishedBlog = response.data.blog;

        if (publishedBlog.status !== 'published') {
          throw new Error('Blog should be published');
        }

        if (!publishedBlog.publishedAt) {
          throw new Error('Published date should be set');
        }

        this.testBlog = publishedBlog;
        this.log(`Blog published at: ${publishedBlog.publishedAt}`, 'info');
      });

      // Test 5: Public API - Website Information
      await this.test('Public API - Website Information', async () => {
        const response = await axios.get(`${API_BASE_URL}/api/v1/public/website`, {
          headers: { 'X-API-Key': this.testDomain.apiKey }
        });

        if (response.status !== 200) {
          throw new Error(`Expected status 200, got ${response.status}`);
        }

        const website = response.data.website;

        if (website.name !== this.testDomain.name) {
          throw new Error('Website name mismatch');
        }

        if (website.blogCount !== 1) {
          throw new Error(`Expected blog count 1, got ${website.blogCount}`);
        }

        this.log(`Website info retrieved: ${website.name} (${website.blogCount} blogs)`, 'info');
      });

      // Test 6: Public API - Fetch All Blogs
      await this.test('Public API - Fetch All Blogs', async () => {
        const response = await axios.get(`${API_BASE_URL}/api/v1/public/blogs`, {
          headers: { 'X-API-Key': this.testDomain.apiKey }
        });

        if (response.status !== 200) {
          throw new Error(`Expected status 200, got ${response.status}`);
        }

        const data = response.data;

        if (!data.data || data.data.length !== 1) {
          throw new Error(`Expected 1 blog, got ${data.data?.length || 0}`);
        }

        const blog = data.data[0];

        if (blog.title !== this.testBlog.title) {
          throw new Error('Blog title mismatch');
        }

        if (!blog.author || !blog.author.firstName) {
          throw new Error('Author information missing');
        }

        this.log(`Blogs retrieved: ${data.total} total, page ${data.page}`, 'info');
      });

      // Test 7: Public API - Fetch Single Blog
      await this.test('Public API - Fetch Single Blog', async () => {
        const response = await axios.get(`${API_BASE_URL}/api/v1/public/blogs/${this.testBlog.slug}`, {
          headers: { 'X-API-Key': this.testDomain.apiKey }
        });

        if (response.status !== 200) {
          throw new Error(`Expected status 200, got ${response.status}`);
        }

        const blog = response.data.blog;

        if (blog.title !== this.testBlog.title) {
          throw new Error('Blog title mismatch');
        }

        if (!blog.content) {
          throw new Error('Blog content should be included');
        }

        if (blog.viewCount !== 1) {
          throw new Error(`Expected view count 1, got ${blog.viewCount}`);
        }

        this.log(`Single blog retrieved: ${blog.title} (${blog.viewCount} views)`, 'info');
      });

      // Test 8: Public API - Categories and Tags
      await this.test('Public API - Categories and Tags', async () => {
        // Test categories
        const categoriesResponse = await axios.get(`${API_BASE_URL}/api/v1/public/categories`, {
          headers: { 'X-API-Key': this.testDomain.apiKey }
        });

        if (categoriesResponse.status !== 200) {
          throw new Error(`Expected status 200 for categories, got ${categoriesResponse.status}`);
        }

        const categories = categoriesResponse.data.categories;
        if (!categories.includes('Integration') || !categories.includes('Testing')) {
          throw new Error('Expected categories not found');
        }

        // Test tags
        const tagsResponse = await axios.get(`${API_BASE_URL}/api/v1/public/tags`, {
          headers: { 'X-API-Key': this.testDomain.apiKey }
        });

        if (tagsResponse.status !== 200) {
          throw new Error(`Expected status 200 for tags, got ${tagsResponse.status}`);
        }

        const tags = tagsResponse.data.tags;
        if (!tags.includes('integration') || !tags.includes('test')) {
          throw new Error('Expected tags not found');
        }

        this.log(`Categories: ${categories.length}, Tags: ${tags.length}`, 'info');
      });

      // Test 9: Public API - Share Count
      await this.test('Public API - Share Count', async () => {
        const response = await axios.post(`${API_BASE_URL}/api/v1/public/blogs/${this.testBlog._id}/share`, {}, {
          headers: { 'X-API-Key': this.testDomain.apiKey }
        });

        if (response.status !== 200 && response.status !== 201) {
          throw new Error(`Expected status 200 or 201, got ${response.status}`);
        }

        // Verify share count was incremented
        const blogResponse = await axios.get(`${API_BASE_URL}/api/v1/public/blogs/${this.testBlog.slug}`, {
          headers: { 'X-API-Key': this.testDomain.apiKey }
        });

        const blog = blogResponse.data.blog;
        if (blog.shareCount !== 1) {
          throw new Error(`Expected share count 1, got ${blog.shareCount}`);
        }

        this.log(`Share count incremented: ${blog.shareCount}`, 'info');
      });

      this.log('🎉 All tests passed! Complete integration flow working correctly.', 'success');

    } catch (error) {
      this.log(`💥 Test suite failed: ${error.message}`, 'error');
      throw error;
    } finally {
      await this.cleanup();
      this.printSummary();
    }
  }

  async cleanup() {
    this.log('🧹 Cleaning up test data...', 'info');
    
    try {
      if (this.testBlog && this.testUserToken) {
        await axios.delete(`${API_BASE_URL}/api/v1/blogs/${this.testBlog._id}`, {
          headers: { Authorization: `Bearer ${this.testUserToken}` }
        });
        this.log('Blog deleted', 'info');
      }

      if (this.testDomain && this.testUserToken) {
        await axios.delete(`${API_BASE_URL}/api/v1/websites/${this.testDomain._id}`, {
          headers: { Authorization: `Bearer ${this.testUserToken}` }
        });
        this.log('Website deleted', 'info');
      }
    } catch (error) {
      this.log(`Cleanup error: ${error.message}`, 'warning');
    }
  }

  printSummary() {
    this.log('📊 Test Summary', 'info');
    this.log('=' .repeat(60), 'info');
    
    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = this.testResults.filter(r => r.status === 'FAILED').length;
    const total = this.testResults.length;

    this.log(`Total Tests: ${total}`, 'info');
    this.log(`Passed: ${passed}`, 'success');
    this.log(`Failed: ${failed}`, failed > 0 ? 'error' : 'info');

    if (failed === 0) {
      this.log('🎉 ALL TESTS PASSED! Integration is working perfectly.', 'success');
    } else {
      this.log('❌ Some tests failed. Check the logs above for details.', 'error');
    }

    this.log('=' .repeat(60), 'info');
  }
}

// Run the test
async function main() {
  const tester = new IntegrationTester();
  try {
    await tester.runCompleteIntegrationTest();
    process.exit(0);
  } catch (error) {
    console.error('Test suite failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = IntegrationTester;
