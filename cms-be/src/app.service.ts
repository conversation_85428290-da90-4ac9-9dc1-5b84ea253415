import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  constructor(private configService: ConfigService) {}

  getHealth() {
    return {
      message: 'CreaSoft CMS API is running successfully!',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: this.configService.get('NODE_ENV', 'development'),
    };
  }

  getStatus() {
    return {
      api: {
        name: 'CreaSoft CMS API',
        version: '1.0.0',
        description: 'Multi-website blog management CMS API',
        environment: this.configService.get('NODE_ENV', 'development'),
        port: this.configService.get('PORT', 8000),
      },
      database: {
        type: 'MongoDB',
        connected: true, // This will be enhanced later with actual connection check
      },
      services: {
        cloudinary: {
          configured: !!this.configService.get('CLOUDINARY_CLOUD_NAME'),
        },
        jwt: {
          configured: !!this.configService.get('JWT_SECRET'),
        },
      },
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }
}
