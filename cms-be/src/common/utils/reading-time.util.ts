export function calculateReadingTime(content: string): number {
  // Remove HTML tags and get plain text
  const plainText = content.replace(/<[^>]*>/g, '');
  
  // Count words (split by whitespace and filter out empty strings)
  const wordCount = plainText.split(/\s+/).filter(word => word.length > 0).length;
  
  // Average reading speed is 225 words per minute
  const wordsPerMinute = 225;
  
  // Calculate reading time in minutes, minimum 1 minute
  return Math.max(1, Math.ceil(wordCount / wordsPerMinute));
}