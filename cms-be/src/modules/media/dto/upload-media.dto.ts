import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsArray } from 'class-validator';

export class UploadMediaDto {
  @ApiProperty({ 
    description: 'Alt text for the media',
    required: false 
  })
  @IsOptional()
  @IsString()
  alt?: string;

  @ApiProperty({ 
    description: 'Caption for the media',
    required: false 
  })
  @IsOptional()
  @IsString()
  caption?: string;

  @ApiProperty({ 
    description: 'Tags for the media',
    type: [String],
    required: false 
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ 
    description: 'Folder to organize media',
    required: false 
  })
  @IsOptional()
  @IsString()
  folder?: string;
}

export class UpdateMediaDto {
  @ApiProperty({ 
    description: 'Alt text for the media',
    required: false 
  })
  @IsOptional()
  @IsString()
  alt?: string;

  @ApiProperty({ 
    description: 'Caption for the media',
    required: false 
  })
  @IsOptional()
  @IsString()
  caption?: string;

  @ApiProperty({ 
    description: 'Tags for the media',
    type: [String],
    required: false 
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}
