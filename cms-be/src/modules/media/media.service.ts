import { 
  Injectable, 
  NotFoundException, 
  BadRequestException,
  ForbiddenException 
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { Model } from 'mongoose';
import { v2 as cloudinary } from 'cloudinary';
import { Media, MediaDocument, MediaType } from './schemas/media.schema';
import { UploadMediaDto, UpdateMediaDto } from './dto/upload-media.dto';
import { QueryDto } from '../../common/dto/query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination.dto';
import { UserDocument } from '../users/schemas/user.schema';
import { UserRole } from '../../common/decorators/roles.decorator';

@Injectable()
export class MediaService {
  constructor(
    @InjectModel(Media.name) private mediaModel: Model<MediaDocument>,
    private configService: ConfigService,
  ) {
    // Configure Cloudinary
    cloudinary.config({
      cloud_name: this.configService.get('CLOUDINARY_CLOUD_NAME'),
      api_key: this.configService.get('CLOUDINARY_API_KEY'),
      api_secret: this.configService.get('CLOUDINARY_API_SECRET'),
    });
  }

  async uploadFile(
    file: Express.Multer.File,
    uploadMediaDto: UploadMediaDto,
    currentUser: UserDocument,
  ): Promise<MediaDocument> {
    try {
      // Validate file type
      const allowedTypes = this.configService
        .get('ALLOWED_FILE_TYPES', 'image/jpeg,image/png,image/gif,image/webp')
        .split(',');
      
      if (!allowedTypes.includes(file.mimetype)) {
        throw new BadRequestException('File type not allowed');
      }

      // Validate file size
      const maxSize = this.configService.get('MAX_FILE_SIZE', 10485760); // 10MB
      if (file.size > maxSize) {
        throw new BadRequestException('File size too large');
      }

      // Determine media type
      const mediaType = this.getMediaType(file.mimetype);

      // Create folder path
      const folder = uploadMediaDto.folder || 
        `creasoft/${currentUser._id}/${mediaType}s`;

      // Upload to Cloudinary
      const uploadResult = await new Promise((resolve, reject) => {
        cloudinary.uploader.upload_stream(
          {
            folder,
            resource_type: 'auto',
            quality: 'auto',
            fetch_format: 'auto',
          },
          (error, result) => {
            if (error) reject(error);
            else resolve(result);
          }
        ).end(file.buffer);
      }) as any;

      // Save media record to database
      const media = new this.mediaModel({
        filename: uploadResult.public_id.split('/').pop(),
        originalName: file.originalname,
        url: uploadResult.secure_url,
        publicId: uploadResult.public_id,
        type: mediaType,
        mimeType: file.mimetype,
        size: file.size,
        width: uploadResult.width,
        height: uploadResult.height,
        uploadedBy: currentUser._id,
        alt: uploadMediaDto.alt,
        caption: uploadMediaDto.caption,
        tags: uploadMediaDto.tags || [],
        folder: uploadResult.folder,
        metadata: {
          format: uploadResult.format,
          resourceType: uploadResult.resource_type,
          bytes: uploadResult.bytes,
          etag: uploadResult.etag,
        },
      });

      return media.save();
    } catch (error) {
      throw new BadRequestException(`Upload failed: ${error.message}`);
    }
  }

  async findAll(
    query: QueryDto,
    currentUser: UserDocument,
    type?: MediaType,
  ): Promise<PaginationResponseDto<MediaDocument>> {
    const { page = 1, limit = 20, search, sortBy = 'createdAt', sortOrder = 'desc' } = query;
    
    const filter: any = { isActive: true };
    
    // Non-admin users can only see their own media
    if (currentUser.role !== UserRole.ADMIN) {
      filter.uploadedBy = currentUser._id;
    }
    
    if (type) {
      filter.type = type;
    }
    
    if (search) {
      filter.$or = [
        { originalName: { $regex: search, $options: 'i' } },
        { alt: { $regex: search, $options: 'i' } },
        { caption: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [media, total] = await Promise.all([
      this.mediaModel
        .find(filter)
        .populate('uploadedBy', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.mediaModel.countDocuments(filter),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: media,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findOne(id: string, currentUser: UserDocument): Promise<MediaDocument> {
    const media = await this.mediaModel
      .findById(id)
      .populate('uploadedBy', 'firstName lastName email');
    
    if (!media) {
      throw new NotFoundException('Media not found');
    }

    // Check permissions
    if (
      currentUser.role !== UserRole.ADMIN && 
      media.uploadedBy._id.toString() !== currentUser._id.toString()
    ) {
      throw new ForbiddenException('You can only access your own media');
    }

    return media;
  }

  async update(
    id: string,
    updateMediaDto: UpdateMediaDto,
    currentUser: UserDocument,
  ): Promise<MediaDocument> {
    const media = await this.findOne(id, currentUser);

    Object.assign(media, updateMediaDto);
    return media.save();
  }

  async remove(id: string, currentUser: UserDocument): Promise<void> {
    const media = await this.findOne(id, currentUser);

    try {
      // Delete from Cloudinary
      await cloudinary.uploader.destroy(media.publicId);
    } catch (error) {
      console.error('Failed to delete from Cloudinary:', error);
      // Continue with database deletion even if Cloudinary deletion fails
    }

    // Soft delete from database
    media.isActive = false;
    await media.save();
  }

  async getStats(currentUser: UserDocument): Promise<any> {
    const filter: any = { isActive: true };
    
    // Non-admin users can only see their own media stats
    if (currentUser.role !== UserRole.ADMIN) {
      filter.uploadedBy = currentUser._id;
    }

    const [total, byType, totalSize] = await Promise.all([
      this.mediaModel.countDocuments(filter),
      this.mediaModel.aggregate([
        { $match: filter },
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 },
          },
        },
      ]),
      this.mediaModel.aggregate([
        { $match: filter },
        { $group: { _id: null, totalSize: { $sum: '$size' } } }
      ]),
    ]);

    return {
      total,
      byType: byType.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      totalSize: totalSize[0]?.totalSize || 0,
      totalSizeMB: Math.round((totalSize[0]?.totalSize || 0) / 1024 / 1024 * 100) / 100,
    };
  }

  private getMediaType(mimeType: string): MediaType {
    if (mimeType.startsWith('image/')) return MediaType.IMAGE;
    if (mimeType.startsWith('video/')) return MediaType.VIDEO;
    if (mimeType.startsWith('audio/')) return MediaType.AUDIO;
    return MediaType.DOCUMENT;
  }

  async generateOptimizedUrl(
    publicId: string,
    options: {
      width?: number;
      height?: number;
      quality?: string;
      format?: string;
    } = {}
  ): Promise<string> {
    return cloudinary.url(publicId, {
      ...options,
      secure: true,
      fetch_format: 'auto',
      quality: options.quality || 'auto',
    });
  }

  async getUserMedia(userId: string, limit = 10): Promise<MediaDocument[]> {
    return this.mediaModel
      .find({ uploadedBy: userId, isActive: true, type: MediaType.IMAGE })
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('url alt caption originalName')
      .exec();
  }
}
