import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { MediaService } from './media.service';
import { UploadMediaDto, UpdateMediaDto } from './dto/upload-media.dto';
import { QueryDto } from '../../common/dto/query.dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { UserDocument } from '../users/schemas/user.schema';
import { MediaType } from './schemas/media.schema';

@ApiTags('Media')
@Controller('media')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class MediaController {
  constructor(private readonly mediaService: MediaService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a media file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        alt: { type: 'string' },
        caption: { type: 'string' },
        tags: { type: 'array', items: { type: 'string' } },
        folder: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'File uploaded successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid file or upload failed' })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadMediaDto: UploadMediaDto,
    @CurrentUser() currentUser: UserDocument,
  ) {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const media = await this.mediaService.uploadFile(file, uploadMediaDto, currentUser);
    return {
      media,
      message: 'File uploaded successfully',
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all media files with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  @ApiQuery({ name: 'type', required: false, enum: MediaType })
  @ApiResponse({
    status: 200,
    description: 'Media files retrieved successfully',
  })
  async findAll(
    @Query() query: QueryDto,
    @Query('type') type: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    // Convert empty string to undefined for proper filtering
    const mediaType = type && type.trim() !== '' ? type as MediaType : undefined;
    const result = await this.mediaService.findAll(query, currentUser, mediaType);
    return {
      ...result,
      message: 'Media files retrieved successfully',
    };
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get media statistics' })
  @ApiResponse({
    status: 200,
    description: 'Media statistics retrieved successfully',
  })
  async getStats(@CurrentUser() currentUser: UserDocument) {
    const stats = await this.mediaService.getStats(currentUser);
    return {
      stats,
      message: 'Media statistics retrieved successfully',
    };
  }

  @Get('my-recent')
  @ApiOperation({ summary: 'Get current user recent media' })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiResponse({
    status: 200,
    description: 'Recent media retrieved successfully',
  })
  async getMyRecentMedia(
    @Query('limit') limit: number = 10,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const media = await this.mediaService.getUserMedia(
      currentUser._id.toString(),
      limit
    );
    return {
      media,
      message: 'Recent media retrieved successfully',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get media file by ID' })
  @ApiParam({ name: 'id', description: 'Media ID' })
  @ApiResponse({
    status: 200,
    description: 'Media file retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async findOne(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const media = await this.mediaService.findOne(id, currentUser);
    return {
      media,
      message: 'Media file retrieved successfully',
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update media file metadata' })
  @ApiParam({ name: 'id', description: 'Media ID' })
  @ApiResponse({
    status: 200,
    description: 'Media file updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async update(
    @Param('id') id: string,
    @Body() updateMediaDto: UpdateMediaDto,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const media = await this.mediaService.update(id, updateMediaDto, currentUser);
    return {
      media,
      message: 'Media file updated successfully',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete media file' })
  @ApiParam({ name: 'id', description: 'Media ID' })
  @ApiResponse({
    status: 200,
    description: 'Media file deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async remove(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    await this.mediaService.remove(id, currentUser);
    return {
      message: 'Media file deleted successfully',
    };
  }

  @Post(':id/optimize')
  @ApiOperation({ summary: 'Generate optimized URL for media' })
  @ApiParam({ name: 'id', description: 'Media ID' })
  @ApiResponse({
    status: 200,
    description: 'Optimized URL generated successfully',
  })
  async generateOptimizedUrl(
    @Param('id') id: string,
    @Body() options: {
      width?: number;
      height?: number;
      quality?: string;
      format?: string;
    },
    @CurrentUser() currentUser: UserDocument,
  ) {
    const media = await this.mediaService.findOne(id, currentUser);
    const optimizedUrl = await this.mediaService.generateOptimizedUrl(
      media.publicId,
      options
    );
    
    return {
      optimizedUrl,
      message: 'Optimized URL generated successfully',
    };
  }
}
