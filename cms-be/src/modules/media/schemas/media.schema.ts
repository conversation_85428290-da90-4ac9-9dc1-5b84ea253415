import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from '../../users/schemas/user.schema';

export type MediaDocument = Media & Document;

export enum MediaType {
  IMAGE = 'image',
  VIDEO = 'video',
  DOCUMENT = 'document',
  AUDIO = 'audio',
}

@Schema({ timestamps: true })
export class Media {
  @Prop({ required: true })
  filename: string;

  @Prop({ required: true })
  originalName: string;

  @Prop({ required: true })
  url: string;

  @Prop({ required: true })
  publicId: string; // Cloudinary public ID

  @Prop({ 
    type: String, 
    enum: Object.values(MediaType), 
    required: true 
  })
  type: MediaType;

  @Prop({ required: true })
  mimeType: string;

  @Prop({ required: true })
  size: number; // in bytes

  @Prop({ type: Number })
  width?: number;

  @Prop({ type: Number })
  height?: number;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  uploadedBy: Types.ObjectId;

  @Prop({ type: String })
  alt?: string;

  @Prop({ type: String })
  caption?: string;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: String })
  folder?: string; // Cloudinary folder

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  @Prop({ default: true })
  isActive: boolean;
}

export const MediaSchema = SchemaFactory.createForClass(Media);

// Indexes for performance
MediaSchema.index({ uploadedBy: 1 });
MediaSchema.index({ type: 1 });
MediaSchema.index({ publicId: 1 });
MediaSchema.index({ createdAt: -1 });
MediaSchema.index({ tags: 1 });
MediaSchema.index({ isActive: 1 });

// Compound indexes
MediaSchema.index({ uploadedBy: 1, type: 1 });
MediaSchema.index({ uploadedBy: 1, isActive: 1, createdAt: -1 });
