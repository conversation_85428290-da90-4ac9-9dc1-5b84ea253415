import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from '../../users/schemas/user.schema';
import { Website } from '../../websites/schemas/website.schema';

export type BlogDocument = Blog & Document;

export enum BlogStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  SCHEDULED = 'scheduled',
  ARCHIVED = 'archived',
}

@Schema({ _id: false })
export class SeoData {
  @Prop({ type: String })
  seoTitle?: string;

  @Prop({ type: String })
  seoDescription?: string;

  @Prop({ type: String })
  focusKeyword?: string;

  @Prop({ type: String })
  canonicalUrl?: string;

  @Prop({ type: String })
  ogTitle?: string;

  @Prop({ type: String })
  ogDescription?: string;

  @Prop({ type: String })
  ogImage?: string;

  @Prop({ type: Boolean, default: false })
  noIndex: boolean;

  @Prop({ type: Boolean, default: false })
  noFollow: boolean;

  @Prop({ type: Number, min: 0, max: 100 })
  seoScore?: number;
}

const SeoDataSchema = SchemaFactory.createForClass(SeoData);

@Schema({ timestamps: true })
export class Blog {
  @Prop({ required: true, trim: true })
  title: string;

  @Prop({ required: true, trim: true, lowercase: true })
  slug: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: false, trim: true })
  excerpt?: string;

  @Prop({ type: String })
  featuredImage?: string;

  @Prop({ type: Types.ObjectId, ref: 'Website', required: true })
  websiteId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  authorId: Types.ObjectId;

  @Prop({ 
    type: String, 
    enum: Object.values(BlogStatus), 
    default: BlogStatus.DRAFT 
  })
  status: BlogStatus;

  @Prop({ type: SeoDataSchema, default: {} })
  seo: SeoData;

  @Prop({ type: [String], default: [] })
  categories: string[];

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: Date })
  publishedAt?: Date;

  @Prop({ type: Date })
  scheduledAt?: Date;

  @Prop({ type: Number, default: 0 })
  viewCount: number;

  @Prop({ type: Number, default: 0 })
  shareCount: number;

  @Prop({ type: Number, default: 0 })
  readingTime: number; // in minutes

  @Prop({ type: Boolean, default: false })
  isFeatured: boolean;

  @Prop({ type: Boolean, default: true })
  allowComments: boolean;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  @Prop({ type: Date })
  deletedAt?: Date;
}

export const BlogSchema = SchemaFactory.createForClass(Blog);

// Indexes for performance
BlogSchema.index({ slug: 1, websiteId: 1 }, { unique: true });
BlogSchema.index({ websiteId: 1 });
BlogSchema.index({ authorId: 1 });
BlogSchema.index({ status: 1 });
BlogSchema.index({ publishedAt: -1 });
BlogSchema.index({ createdAt: -1 });
BlogSchema.index({ categories: 1 });
BlogSchema.index({ tags: 1 });
BlogSchema.index({ isFeatured: 1 });

// Compound indexes for common queries
BlogSchema.index({ websiteId: 1, status: 1, publishedAt: -1 });
BlogSchema.index({ websiteId: 1, categories: 1, status: 1 });
BlogSchema.index({ websiteId: 1, tags: 1, status: 1 });
BlogSchema.index({ authorId: 1, status: 1, createdAt: -1 });
