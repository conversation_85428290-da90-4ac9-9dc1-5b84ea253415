import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { WebsitesService } from './websites.service';
import { CreateWebsiteDto } from './dto/create-website.dto';
import { UpdateWebsiteDto } from './dto/update-website.dto';
import { QueryDto } from '../../common/dto/query.dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles, UserRole } from '../../common/decorators/roles.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { UserDocument } from '../users/schemas/user.schema';

@ApiTags('Websites')
@Controller('websites')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class WebsitesController {
  constructor(private readonly websitesService: WebsitesService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new website' })
  @ApiResponse({
    status: 201,
    description: 'Website successfully created',
  })
  @ApiResponse({ status: 409, description: 'Website domain already exists' })
  async create(
    @Body() createWebsiteDto: CreateWebsiteDto,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const website = await this.websitesService.create(createWebsiteDto, currentUser);
    return {
      website,
      message: 'Website created successfully',
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all websites with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  @ApiResponse({
    status: 200,
    description: 'Websites retrieved successfully',
  })
  async findAll(
    @Query() query: QueryDto,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const result = await this.websitesService.findAll(query, currentUser);
    return {
      ...result,
      message: 'Websites retrieved successfully',
    };
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get website statistics' })
  @ApiResponse({
    status: 200,
    description: 'Website statistics retrieved successfully',
  })
  async getStats(@CurrentUser() currentUser: UserDocument) {
    const stats = await this.websitesService.getStats(currentUser);
    return {
      stats,
      message: 'Website statistics retrieved successfully',
    };
  }

  @Get('my-websites')
  @ApiOperation({ summary: 'Get current user websites (simplified)' })
  @ApiResponse({
    status: 200,
    description: 'User websites retrieved successfully',
  })
  async getMyWebsites(@CurrentUser() currentUser: UserDocument) {
    console.log('🔍 getMyWebsites called with currentUser:', {
      id: currentUser._id,
      email: currentUser.email,
      role: currentUser.role
    });
    
    const websites = await this.websitesService.getUserWebsites(
      currentUser._id.toString()
    );
    
    console.log('📊 Returning websites:', websites);
    
    return {
      websites,
      message: 'User websites retrieved successfully',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get website by ID' })
  @ApiParam({ name: 'id', description: 'Website ID' })
  @ApiResponse({
    status: 200,
    description: 'Website retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Website not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async findOne(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const website = await this.websitesService.findOne(id, currentUser);
    return {
      website,
      message: 'Website retrieved successfully',
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update website' })
  @ApiParam({ name: 'id', description: 'Website ID' })
  @ApiResponse({
    status: 200,
    description: 'Website updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Website not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 409, description: 'Domain already exists' })
  async update(
    @Param('id') id: string,
    @Body() updateWebsiteDto: UpdateWebsiteDto,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const website = await this.websitesService.update(id, updateWebsiteDto, currentUser);
    return {
      website,
      message: 'Website updated successfully',
    };
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete website' })
  @ApiParam({ name: 'id', description: 'Website ID' })
  @ApiResponse({
    status: 200,
    description: 'Website deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Website not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async remove(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    await this.websitesService.remove(id, currentUser);
    return {
      message: 'Website deleted successfully',
    };
  }

  @Post(':id/regenerate-api-key')
  @ApiOperation({ summary: 'Regenerate website API key' })
  @ApiParam({ name: 'id', description: 'Website ID' })
  @ApiResponse({
    status: 200,
    description: 'API key regenerated successfully',
  })
  @ApiResponse({ status: 404, description: 'Website not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async regenerateApiKey(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const website = await this.websitesService.regenerateApiKey(id, currentUser);
    return {
      website,
      message: 'API key regenerated successfully',
    };
  }
}
