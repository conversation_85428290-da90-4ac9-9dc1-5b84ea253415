import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { WebsitesService } from './websites.service';
import { WebsitesController } from './websites.controller';
import { Website, WebsiteSchema } from './schemas/website.schema';
import { Blog, BlogSchema } from '../blogs/schemas/blog.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Website.name, schema: WebsiteSchema },
      { name: Blog.name, schema: BlogSchema },
    ]),
  ],
  controllers: [WebsitesController],
  providers: [WebsitesService],
  exports: [WebsitesService],
})
export class WebsitesModule {}
