import { IsString, IsEnum, IsOptional, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { NotificationType } from '../schemas/notification.schema';

export class CreateNotificationDto {
  @ApiProperty({ 
    description: 'Notification title',
    example: 'New blog published' 
  })
  @IsString()
  title: string;

  @ApiProperty({ 
    description: 'Notification message',
    example: 'Your blog post "Getting Started" has been published successfully.' 
  })
  @IsString()
  message: string;

  @ApiProperty({ 
    description: 'Notification type',
    enum: NotificationType,
    default: NotificationType.INFO,
    required: false 
  })
  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @ApiProperty({ 
    description: 'Action URL for the notification',
    example: '/blogs/123',
    required: false 
  })
  @IsOptional()
  @IsString()
  actionUrl?: string;

  @ApiProperty({ 
    description: 'Additional metadata',
    required: false 
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
