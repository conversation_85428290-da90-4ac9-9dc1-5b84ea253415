import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { NotificationsService } from './notifications.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { UserDocument } from '../users/schemas/user.schema';

@ApiTags('Notifications')
@Controller('notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new notification' })
  @ApiResponse({
    status: 201,
    description: 'Notification successfully created',
  })
  async create(
    @Body() createNotificationDto: CreateNotificationDto,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const notification = await this.notificationsService.create(
      createNotificationDto,
      currentUser._id.toString()
    );
    return {
      notification,
      message: 'Notification created successfully',
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all notifications for current user' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of notifications to return' })
  @ApiResponse({
    status: 200,
    description: 'Notifications retrieved successfully',
  })
  async findAll(
    @CurrentUser() currentUser: UserDocument,
    @Query('limit') limit?: string,
  ) {
    const notifications = await this.notificationsService.findAllForUser(
      currentUser._id.toString(),
      limit ? parseInt(limit) : 50
    );
    return {
      data: notifications,
      total: notifications.length,
    };
  }

  @Get('unread-count')
  @ApiOperation({ summary: 'Get unread notification count' })
  @ApiResponse({
    status: 200,
    description: 'Unread count retrieved successfully',
  })
  async getUnreadCount(@CurrentUser() currentUser: UserDocument) {
    const count = await this.notificationsService.getUnreadCount(
      currentUser._id.toString()
    );
    return {
      count,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific notification' })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  @ApiResponse({
    status: 200,
    description: 'Notification retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  async findOne(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const notification = await this.notificationsService.findOne(
      id,
      currentUser._id.toString()
    );
    return {
      notification,
    };
  }

  @Patch(':id/read')
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  @ApiResponse({
    status: 200,
    description: 'Notification marked as read',
  })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  async markAsRead(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const notification = await this.notificationsService.markAsRead(
      id,
      currentUser._id.toString()
    );
    return {
      notification,
      message: 'Notification marked as read',
    };
  }

  @Patch('read-all')
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({
    status: 200,
    description: 'All notifications marked as read',
  })
  async markAllAsRead(@CurrentUser() currentUser: UserDocument) {
    await this.notificationsService.markAllAsRead(currentUser._id.toString());
    return {
      message: 'All notifications marked as read',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a notification' })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  @ApiResponse({
    status: 200,
    description: 'Notification deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  async remove(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    await this.notificationsService.remove(id, currentUser._id.toString());
    return {
      message: 'Notification deleted successfully',
    };
  }
}
