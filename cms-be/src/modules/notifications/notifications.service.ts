import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Notification, NotificationDocument, NotificationType } from './schemas/notification.schema';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { UserDocument } from '../users/schemas/user.schema';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectModel(Notification.name) private notificationModel: Model<NotificationDocument>,
  ) {}

  async create(
    createNotificationDto: CreateNotificationDto,
    userId: string
  ): Promise<NotificationDocument> {
    const notification = new this.notificationModel({
      ...createNotificationDto,
      userId,
    });

    return notification.save();
  }

  async createForUser(
    userId: string,
    title: string,
    message: string,
    type: NotificationType = NotificationType.INFO,
    actionUrl?: string,
    metadata?: Record<string, any>
  ): Promise<NotificationDocument> {
    const notification = new this.notificationModel({
      title,
      message,
      type,
      userId,
      actionUrl,
      metadata,
    });

    return notification.save();
  }

  async findAllForUser(
    userId: string,
    limit: number = 50
  ): Promise<NotificationDocument[]> {
    return this.notificationModel
      .find({ userId })
      .sort({ createdAt: -1 })
      .limit(limit)
      .exec();
  }

  async findOne(id: string, userId: string): Promise<NotificationDocument> {
    const notification = await this.notificationModel
      .findOne({ _id: id, userId })
      .exec();

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    return notification;
  }

  async markAsRead(id: string, userId: string): Promise<NotificationDocument> {
    const notification = await this.findOne(id, userId);

    if (!notification.isRead) {
      notification.isRead = true;
      notification.readAt = new Date();
      await notification.save();
    }

    return notification;
  }

  async markAllAsRead(userId: string): Promise<void> {
    await this.notificationModel.updateMany(
      { userId, isRead: false },
      { 
        isRead: true, 
        readAt: new Date() 
      }
    );
  }

  async remove(id: string, userId: string): Promise<void> {
    const notification = await this.findOne(id, userId);
    await this.notificationModel.findByIdAndDelete(notification._id);
  }

  async getUnreadCount(userId: string): Promise<number> {
    return this.notificationModel.countDocuments({ userId, isRead: false });
  }

  async removeOldNotifications(daysOld: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    await this.notificationModel.deleteMany({
      createdAt: { $lt: cutoffDate },
      isRead: true,
    });
  }

  // Helper methods for common notification types
  async notifyBlogPublished(userId: string, blogTitle: string, blogId: string): Promise<void> {
    await this.createForUser(
      userId,
      'Blog Published',
      `Your blog post "${blogTitle}" has been published successfully.`,
      NotificationType.SUCCESS,
      `/blogs/${blogId}`,
      { blogId, action: 'blog_published' }
    );
  }

  async notifyBlogScheduled(userId: string, blogTitle: string, scheduledAt: Date): Promise<void> {
    await this.createForUser(
      userId,
      'Blog Scheduled',
      `Your blog post "${blogTitle}" has been scheduled for ${scheduledAt.toLocaleDateString()}.`,
      NotificationType.INFO,
      undefined,
      { action: 'blog_scheduled', scheduledAt }
    );
  }

  async notifyMediaUploaded(userId: string, fileName: string, mediaId: string): Promise<void> {
    await this.createForUser(
      userId,
      'Media Uploaded',
      `File "${fileName}" has been uploaded successfully.`,
      NotificationType.SUCCESS,
      `/media`,
      { mediaId, action: 'media_uploaded' }
    );
  }

  async notifyUserAdded(userId: string, newUserName: string): Promise<void> {
    await this.createForUser(
      userId,
      'New User Added',
      `User "${newUserName}" has been added to the system.`,
      NotificationType.INFO,
      `/users`,
      { action: 'user_added' }
    );
  }

  async notifyWebsiteCreated(userId: string, websiteName: string, websiteId: string): Promise<void> {
    await this.createForUser(
      userId,
      'Website Created',
      `Website "${websiteName}" has been created successfully.`,
      NotificationType.SUCCESS,
      `/websites/${websiteId}`,
      { websiteId, action: 'website_created' }
    );
  }

  async notifyError(userId: string, title: string, message: string, actionUrl?: string): Promise<void> {
    await this.createForUser(
      userId,
      title,
      message,
      NotificationType.ERROR,
      actionUrl,
      { action: 'error' }
    );
  }
}
