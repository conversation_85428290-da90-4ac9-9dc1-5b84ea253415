import { ApiProperty } from '@nestjs/swagger';
import { 
  IsEmail, 
  IsString, 
  MinLength, 
  IsEnum, 
  IsOptional, 
  IsBoolean 
} from 'class-validator';
import { UserRole } from '../../../common/decorators/roles.decorator';

export class CreateUserDto {
  @ApiProperty({ 
    description: 'User email address',
    example: '<EMAIL>' 
  })
  @IsEmail()
  email: string;

  @ApiProperty({ 
    description: 'User password (minimum 6 characters)',
    example: 'password123',
    minLength: 6 
  })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({ 
    description: 'User first name',
    example: '<PERSON>' 
  })
  @IsString()
  firstName: string;

  @ApiProperty({ 
    description: 'User last name',
    example: 'Doe' 
  })
  @IsString()
  lastName: string;

  @ApiProperty({ 
    description: 'User role',
    enum: UserRole,
    default: UserRole.EDITOR,
    required: false 
  })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @ApiProperty({ 
    description: 'Whether user is active',
    default: true,
    required: false 
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ 
    description: 'User avatar URL',
    required: false 
  })
  @IsOptional()
  @IsString()
  avatar?: string;
}
