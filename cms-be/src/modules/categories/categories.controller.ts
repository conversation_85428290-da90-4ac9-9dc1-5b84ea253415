import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { CategoriesService } from './categories.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { QueryDto } from '../../common/dto/query.dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { UserDocument } from '../users/schemas/user.schema';

@ApiTags('Categories')
@Controller('categories')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new category' })
  @ApiResponse({
    status: 201,
    description: 'Category successfully created',
  })
  @ApiResponse({ status: 409, description: 'Category slug already exists' })
  async create(
    @Body() createCategoryDto: CreateCategoryDto,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const category = await this.categoriesService.create(createCategoryDto, currentUser);
    return {
      category,
      message: 'Category created successfully',
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all categories with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  @ApiQuery({ name: 'websiteId', required: false, type: String })
  @ApiResponse({
    status: 200,
    description: 'Categories retrieved successfully',
  })
  async findAll(
    @Query() query: QueryDto,
    @Query('websiteId') websiteId: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const result = await this.categoriesService.findAll(query, currentUser, websiteId);
    return {
      ...result,
      message: 'Categories retrieved successfully',
    };
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get category statistics' })
  @ApiQuery({ name: 'websiteId', required: false, type: String })
  @ApiResponse({
    status: 200,
    description: 'Category statistics retrieved successfully',
  })
  async getStats(
    @Query('websiteId') websiteId: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const stats = await this.categoriesService.getStats(currentUser, websiteId);
    return {
      stats,
      message: 'Category statistics retrieved successfully',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get category by ID' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiResponse({
    status: 200,
    description: 'Category retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async findOne(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const category = await this.categoriesService.findOne(id, currentUser);
    return {
      category,
      message: 'Category retrieved successfully',
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update category' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiResponse({
    status: 200,
    description: 'Category updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async update(
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const category = await this.categoriesService.update(id, updateCategoryDto, currentUser);
    return {
      category,
      message: 'Category updated successfully',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete category' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiResponse({
    status: 200,
    description: 'Category deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 409, description: 'Category is being used by blogs' })
  async remove(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    await this.categoriesService.remove(id, currentUser);
    return {
      message: 'Category deleted successfully',
    };
  }
}
