import { 
  Injectable, 
  NotFoundException, 
  ConflictException,
  ForbiddenException,
  BadRequestException 
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Category, CategoryDocument } from './schemas/category.schema';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { QueryDto } from '../../common/dto/query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination.dto';
import { UserDocument } from '../users/schemas/user.schema';
import { WebsitesService } from '../websites/websites.service';
import { UserRole } from '../../common/decorators/roles.decorator';
import { generateSlug, generateUniqueSlug } from '../../common/utils/slug.util';

@Injectable()
export class CategoriesService {
  constructor(
    @InjectModel(Category.name) private categoryModel: Model<CategoryDocument>,
    private websitesService: WebsitesService,
  ) {}

  async create(
    createCategoryDto: CreateCategoryDto,
    currentUser: UserDocument
  ): Promise<CategoryDocument> {
    const { websiteId, slug, ...categoryData } = createCategoryDto;

    // Verify website exists and user has access
    await this.websitesService.findOne(websiteId, currentUser);

    // Generate or validate slug
    let finalSlug = slug;
    if (!finalSlug) {
      finalSlug = generateSlug(createCategoryDto.name);
    } else {
      finalSlug = generateSlug(finalSlug);
    }

    // Ensure slug is unique within the website
    const existingSlugs = await this.categoryModel
      .find({ websiteId, slug: { $regex: `^${finalSlug}` } })
      .select('slug')
      .exec();
    
    const slugList = existingSlugs.map(category => category.slug);
    finalSlug = generateUniqueSlug(finalSlug, slugList);

    const category = new this.categoryModel({
      ...categoryData,
      slug: finalSlug,
      websiteId,
      createdBy: currentUser._id,
    });

    return category.save();
  }

  async findAll(
    query: QueryDto,
    currentUser: UserDocument,
    websiteId?: string
  ): Promise<PaginationResponseDto<CategoryDocument>> {
    const { page = 1, limit = 10, search, sortBy = 'name', sortOrder = 'asc' } = query;

    const filter: any = { isActive: true };

    if (websiteId) {
      // Verify user has access to this website
      await this.websitesService.findOne(websiteId, currentUser);
      filter.websiteId = websiteId;
    } else if (currentUser.role !== UserRole.ADMIN) {
      // Non-admin users can only see categories from their websites
      const userWebsites = await this.websitesService.findAll({}, currentUser);
      const websiteIds = userWebsites.data.map(website => website._id);
      filter.websiteId = { $in: websiteIds };
    }

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const skip = (page - 1) * limit;

    const [categories, total] = await Promise.all([
      this.categoryModel
        .find(filter)
        .populate('websiteId', 'name domain')
        .populate('createdBy', 'firstName lastName')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.categoryModel.countDocuments(filter),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: categories,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findOne(id: string, currentUser: UserDocument): Promise<CategoryDocument> {
    // Validate ObjectId format
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid category ID format');
    }

    const category = await this.categoryModel
      .findById(id)
      .populate('websiteId', 'name domain ownerId')
      .populate('createdBy', 'firstName lastName email');
    
    if (!category) {
      throw new NotFoundException('Category not found');
    }

    // Check permissions
    if (
      currentUser.role !== UserRole.ADMIN && 
      (category.websiteId as any).ownerId.toString() !== currentUser._id.toString()
    ) {
      throw new ForbiddenException('You can only access categories from your websites');
    }

    return category;
  }

  async update(
    id: string, 
    updateCategoryDto: UpdateCategoryDto,
    currentUser: UserDocument
  ): Promise<CategoryDocument> {
    const category = await this.findOne(id, currentUser);

    // Update slug if name changed
    if (updateCategoryDto.name && updateCategoryDto.name !== category.name) {
      if (!updateCategoryDto.slug) {
        updateCategoryDto.slug = generateSlug(updateCategoryDto.name);
      }
    }

    // Validate slug uniqueness if changed
    if (updateCategoryDto.slug && updateCategoryDto.slug !== category.slug) {
      const finalSlug = generateSlug(updateCategoryDto.slug);
      const existingSlugs = await this.categoryModel
        .find({ 
          websiteId: category.websiteId, 
          slug: { $regex: `^${finalSlug}` },
          _id: { $ne: id }
        })
        .select('slug')
        .exec();
      
      const slugList = existingSlugs.map(c => c.slug);
      updateCategoryDto.slug = generateUniqueSlug(finalSlug, slugList);
    }

    Object.assign(category, updateCategoryDto);
    return category.save();
  }

  async remove(id: string, currentUser: UserDocument): Promise<void> {
    const category = await this.findOne(id, currentUser);

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    // Check if category is being used by any blogs
    if (category.postCount > 0) {
      throw new ConflictException('Cannot delete category that is being used by blogs');
    }

    // Soft delete - set isActive to false
    category.isActive = false;
    await category.save();
  }

  async findByWebsite(websiteId: string): Promise<CategoryDocument[]> {
    return this.categoryModel
      .find({ websiteId, isActive: true })
      .sort({ name: 1 })
      .exec();
  }

  async incrementPostCount(categoryName: string, websiteId: string): Promise<void> {
    await this.categoryModel
      .updateOne(
        { name: categoryName, websiteId, isActive: true },
        { $inc: { postCount: 1 } }
      )
      .exec();
  }

  async decrementPostCount(categoryName: string, websiteId: string): Promise<void> {
    await this.categoryModel
      .updateOne(
        { name: categoryName, websiteId, isActive: true },
        { $inc: { postCount: -1 } }
      )
      .exec();
  }

  async getStats(currentUser: UserDocument, websiteId?: string): Promise<any> {
    const filter: any = { isActive: true };

    if (websiteId) {
      await this.websitesService.findOne(websiteId, currentUser);
      filter.websiteId = websiteId;
    } else if (currentUser.role !== UserRole.ADMIN) {
      const userWebsites = await this.websitesService.findAll({}, currentUser);
      const websiteIds = userWebsites.data.map(website => website._id);
      filter.websiteId = { $in: websiteIds };
    }

    const [totalCategories, categoriesWithPosts, topCategories] = await Promise.all([
      this.categoryModel.countDocuments(filter),
      this.categoryModel.countDocuments({ ...filter, postCount: { $gt: 0 } }),
      this.categoryModel
        .find(filter)
        .sort({ postCount: -1 })
        .limit(5)
        .select('name postCount color')
        .exec(),
    ]);

    return {
      totalCategories,
      categoriesWithPosts,
      categoriesWithoutPosts: totalCategories - categoriesWithPosts,
      topCategories,
    };
  }
}
