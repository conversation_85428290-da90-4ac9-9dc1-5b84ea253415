import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsOptional, 
  IsMongoId,
  IsHexColor,
  MaxLength,
  MinLength
} from 'class-validator';

export class CreateCategoryDto {
  @ApiProperty({ 
    description: 'Category name',
    example: 'Technology',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @ApiProperty({ 
    description: 'Category slug (URL-friendly)',
    example: 'technology',
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  slug?: string;

  @ApiProperty({ 
    description: 'Category description',
    example: 'Posts about technology and innovation',
    required: false,
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ 
    description: 'Category color (hex code)',
    example: '#3B82F6',
    required: false
  })
  @IsOptional()
  @IsHexColor()
  color?: string;

  @ApiProperty({ 
    description: 'Website ID this category belongs to' 
  })
  @IsMongoId()
  websiteId: string;
}
