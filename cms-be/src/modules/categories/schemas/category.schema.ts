import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from '../../users/schemas/user.schema';
import { Website } from '../../websites/schemas/website.schema';

export type CategoryDocument = Category & Document;

@Schema({ timestamps: true })
export class Category {
  @Prop({ required: true, trim: true })
  name: string;

  @Prop({ required: true, trim: true, lowercase: true })
  slug: string;

  @Prop({ type: String, trim: true })
  description?: string;

  @Prop({ type: String, default: '#3B82F6' })
  color: string;

  @Prop({ type: Types.ObjectId, ref: 'Website', required: true })
  websiteId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  createdBy: Types.ObjectId;

  @Prop({ type: Number, default: 0 })
  postCount: number;

  @Prop({ type: Boolean, default: true })
  isActive: boolean;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;
}

export const CategorySchema = SchemaFactory.createForClass(Category);

// Indexes for performance
CategorySchema.index({ slug: 1, websiteId: 1 }, { unique: true });
CategorySchema.index({ websiteId: 1 });
CategorySchema.index({ createdBy: 1 });
CategorySchema.index({ isActive: 1 });
CategorySchema.index({ name: 'text', description: 'text' });

// Compound indexes for common queries
CategorySchema.index({ websiteId: 1, isActive: 1, name: 1 });
