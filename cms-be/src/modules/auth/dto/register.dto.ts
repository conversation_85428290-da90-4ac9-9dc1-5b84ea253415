import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, <PERSON><PERSON><PERSON>th, <PERSON>Optional, IsEnum } from 'class-validator';
import { UserRole } from '../../../common/decorators/roles.decorator';

export class RegisterDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User password (minimum 6 characters)',
    example: 'password123',
    minLength: 6,
  })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>',
  })
  @IsString()
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Do<PERSON>',
  })
  @IsString()
  lastName: string;

  @ApiProperty({
    description: 'User role (only admin can set this)',
    enum: UserRole,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;
}
