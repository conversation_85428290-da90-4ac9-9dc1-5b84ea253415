import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import * as request from 'supertest';
import { AppModule } from '../../app.module';
import { BlogStatus } from '../../modules/blogs/schemas/blog.schema';
import { WebsiteTheme } from '../../modules/websites/schemas/website.schema';

describe('Complete Blog Integration Flow', () => {
  let app: INestApplication;
  let mongod: MongoMemoryServer;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(uri),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  }, 30000);

  afterAll(async () => {
    if (app) {
      await app.close();
    }
    if (mongod) {
      await mongod.stop();
    }
  });

  it('should complete the full workflow: user registration → domain creation → blog creation → public API access', async () => {
    console.log('🚀 Starting Complete Integration Flow Test');

    // Step 1: User Registration
    console.log('🔄 Step 1: User Registration');
    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Flow',
      lastName: 'Test',
    };

    const registerResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/register')
      .send(userData)
      .expect(201);

    const testUser = registerResponse.body.user;
    const testUserToken = registerResponse.body.token;

    expect(testUser).toBeDefined();
    expect(testUser.email).toBe(userData.email);
    expect(testUserToken).toBeDefined();
    console.log('✅ User registered successfully:', testUser.email);

    // Step 2: Domain/Website Creation
    console.log('🔄 Step 2: Domain/Website Creation');
    const domainData = {
      name: 'Flow Test Website',
      domain: 'flowtest.com',
      description: 'A test website for complete flow testing',
      theme: WebsiteTheme.MINIMAL,
    };

    const domainResponse = await request(app.getHttpServer())
      .post('/api/v1/websites')
      .set('Authorization', `Bearer ${testUserToken}`)
      .send(domainData)
      .expect(201);

    const testDomain = domainResponse.body.website;

    expect(testDomain).toBeDefined();
    expect(testDomain.name).toBe(domainData.name);
    expect(testDomain.domain).toBe(domainData.domain);
    expect(testDomain.apiKey).toBeDefined();
    expect(testDomain.apiKey).toMatch(/^creasoft_[a-f0-9]{32}$/);
    expect(testDomain.ownerId).toBe(testUser._id);
    expect(testDomain.isActive).toBe(true);
    expect(testDomain.blogCount).toBe(0);
    console.log('✅ Domain created successfully:', testDomain.domain);
    console.log('🔑 API Key generated:', testDomain.apiKey);

    // Step 3: Blog Creation
    console.log('🔄 Step 3: Blog Creation');
    const testBlogData = {
      title: 'Complete Flow Test Blog Post',
      slug: 'complete-flow-test-blog-post',
      content: '<h1>Welcome to Complete Flow Testing</h1><p>This is a comprehensive test blog post created during complete flow testing. It contains <strong>rich content</strong> with various HTML elements to test the complete flow from user registration to public API consumption.</p><p>This blog post validates the entire integration pipeline.</p>',
      excerpt: 'A comprehensive test blog post for complete flow testing with rich content and public API validation.',
      websiteId: testDomain._id,
      categories: ['Flow', 'Testing', 'Integration'],
      tags: ['flow', 'test', 'integration', 'complete'],
      isFeatured: true,
      allowComments: true,
      seo: {
        metaTitle: 'Complete Flow Test Blog - Full Integration Testing',
        metaDescription: 'A comprehensive complete flow test blog post to validate the entire blog creation and public API integration.',
        keywords: ['flow', 'testing', 'integration', 'blog', 'complete'],
      },
    };

    const blogResponse = await request(app.getHttpServer())
      .post('/api/v1/blogs')
      .set('Authorization', `Bearer ${testUserToken}`)
      .send(testBlogData)
      .expect(201);

    const createdBlog = blogResponse.body.blog;

    expect(createdBlog).toBeDefined();
    expect(createdBlog.title).toBe(testBlogData.title);
    expect(createdBlog.slug).toBe(testBlogData.slug);
    expect(createdBlog.content).toBe(testBlogData.content);
    expect(createdBlog.excerpt).toBe(testBlogData.excerpt);
    expect(createdBlog.websiteId).toBe(testDomain._id);
    expect(createdBlog.authorId).toBe(testUser._id);
    expect(createdBlog.status).toBe(BlogStatus.DRAFT);
    expect(createdBlog.categories).toEqual(testBlogData.categories);
    expect(createdBlog.tags).toEqual(testBlogData.tags);
    expect(createdBlog.isFeatured).toBe(true);
    expect(createdBlog.allowComments).toBe(true);
    expect(createdBlog.readingTime).toBeGreaterThan(0);
    expect(createdBlog.viewCount).toBe(0);
    expect(createdBlog.shareCount).toBe(0);
    expect(createdBlog.seo).toBeDefined();
    expect(createdBlog.seo.metaTitle).toBe(testBlogData.seo.metaTitle);
    console.log('✅ Blog created successfully:', createdBlog.title);
    console.log('📊 Reading time calculated:', createdBlog.readingTime, 'minutes');

    // Step 4: Blog Publishing
    console.log('🔄 Step 4: Blog Publishing');
    const publishResponse = await request(app.getHttpServer())
      .post(`/api/v1/blogs/${createdBlog._id}/publish`)
      .set('Authorization', `Bearer ${testUserToken}`)
      .expect(200);

    const publishedBlog = publishResponse.body.blog;

    expect(publishedBlog.status).toBe(BlogStatus.PUBLISHED);
    expect(publishedBlog.publishedAt).toBeDefined();
    expect(new Date(publishedBlog.publishedAt)).toBeInstanceOf(Date);
    console.log('✅ Blog published successfully at:', publishedBlog.publishedAt);

    // Verify website blog count updated
    const updatedDomainResponse = await request(app.getHttpServer())
      .get(`/api/v1/websites/${testDomain._id}`)
      .set('Authorization', `Bearer ${testUserToken}`)
      .expect(200);

    expect(updatedDomainResponse.body.website.blogCount).toBe(1);
    console.log('✅ Website blog count updated:', updatedDomainResponse.body.website.blogCount);

    // Step 5: Public API - Website Information
    console.log('🔄 Step 5: Public API - Website Information');
    const websiteInfoResponse = await request(app.getHttpServer())
      .get('/api/v1/public/website')
      .set('X-API-Key', testDomain.apiKey)
      .expect(200);

    const websiteInfo = websiteInfoResponse.body.website;
    expect(websiteInfo).toBeDefined();
    expect(websiteInfo.name).toBe(testDomain.name);
    expect(websiteInfo.domain).toBe(testDomain.domain);
    expect(websiteInfo.description).toBe(testDomain.description);
    expect(websiteInfo.theme).toBe(testDomain.theme);
    expect(websiteInfo.blogCount).toBe(1);
    expect(websiteInfo.isActive).toBe(true);
    console.log('✅ Website info retrieved via public API:', websiteInfo.name);

    // Step 6: Public API - Fetch All Blogs
    console.log('🔄 Step 6: Public API - Fetch All Blogs');
    const allBlogsResponse = await request(app.getHttpServer())
      .get('/api/v1/public/blogs')
      .set('X-API-Key', testDomain.apiKey)
      .expect(200);

    expect(allBlogsResponse.body.data).toBeDefined();
    expect(allBlogsResponse.body.data.length).toBe(1);
    expect(allBlogsResponse.body.total).toBe(1);
    expect(allBlogsResponse.body.page).toBe(1);
    expect(allBlogsResponse.body.limit).toBe(10);
    expect(allBlogsResponse.body.totalPages).toBe(1);
    expect(allBlogsResponse.body.hasNext).toBe(false);
    expect(allBlogsResponse.body.hasPrev).toBe(false);

    const blogFromList = allBlogsResponse.body.data[0];
    expect(blogFromList.id).toBeDefined();
    expect(blogFromList.title).toBe(testBlogData.title);
    expect(blogFromList.slug).toBe(testBlogData.slug);
    expect(blogFromList.excerpt).toBe(testBlogData.excerpt);
    expect(blogFromList.publishedAt).toBeDefined();
    expect(blogFromList.categories).toEqual(testBlogData.categories);
    expect(blogFromList.tags).toEqual(testBlogData.tags);
    expect(blogFromList.readingTime).toBeGreaterThan(0);
    expect(blogFromList.viewCount).toBe(0);
    expect(blogFromList.shareCount).toBe(0);
    expect(blogFromList.isFeatured).toBe(true);
    expect(blogFromList.author).toBeDefined();
    expect(blogFromList.author.firstName).toBe(testUser.firstName);
    expect(blogFromList.author.lastName).toBe(testUser.lastName);
    expect(blogFromList.seo).toBeDefined();
    console.log('✅ All blogs fetched via public API:', blogFromList.title);

    // Step 7: Public API - Fetch Single Blog by Slug
    console.log('🔄 Step 7: Public API - Fetch Single Blog by Slug');
    const singleBlogResponse = await request(app.getHttpServer())
      .get(`/api/v1/public/blogs/${testBlogData.slug}`)
      .set('X-API-Key', testDomain.apiKey)
      .expect(200);

    const singleBlog = singleBlogResponse.body.blog;
    expect(singleBlog).toBeDefined();
    expect(singleBlog.id).toBeDefined();
    expect(singleBlog.title).toBe(testBlogData.title);
    expect(singleBlog.slug).toBe(testBlogData.slug);
    expect(singleBlog.content).toBe(testBlogData.content);
    expect(singleBlog.excerpt).toBe(testBlogData.excerpt);
    expect(singleBlog.publishedAt).toBeDefined();
    expect(singleBlog.categories).toEqual(testBlogData.categories);
    expect(singleBlog.tags).toEqual(testBlogData.tags);
    expect(singleBlog.readingTime).toBeGreaterThan(0);
    expect(singleBlog.viewCount).toBe(1); // Should increment view count
    expect(singleBlog.shareCount).toBe(0);
    expect(singleBlog.isFeatured).toBe(true);
    expect(singleBlog.allowComments).toBe(true);
    expect(singleBlog.author).toBeDefined();
    expect(singleBlog.author.firstName).toBe(testUser.firstName);
    expect(singleBlog.author.lastName).toBe(testUser.lastName);
    expect(singleBlog.seo).toBeDefined();
    expect(singleBlog.seo.metaTitle).toBe(testBlogData.seo.metaTitle);
    expect(singleBlog.seo.metaDescription).toBe(testBlogData.seo.metaDescription);
    expect(singleBlog.website).toBeDefined();
    expect(singleBlog.website.name).toBe(testDomain.name);
    expect(singleBlog.website.domain).toBe(testDomain.domain);
    console.log('✅ Single blog fetched via public API:', singleBlog.title);
    console.log('👁️ View count incremented to:', singleBlog.viewCount);

    console.log('🎉 Complete Integration Flow Test PASSED! All steps working correctly.');
  }, 60000);
});
